import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ApplicationSwitcher from '@/components/layout/ApplicationSwitcher';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  Plus,
  Settings,
  Activity,
  Calendar,
  AlertTriangle,
  TrendingUp,
  Wrench,
  Gauge,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap
} from 'lucide-react';

// Import ATM components (to be created)
import ATMDashboard from '@/components/atm/ATMDashboard';
import AssetRegistry from '@/components/atm/AssetRegistry';
import MaintenanceScheduling from '@/components/atm/MaintenanceScheduling';
import IoTMonitoring from '@/components/atm/IoTMonitoring';
import CalibrationManagement from '@/components/atm/CalibrationManagement';
import DowntimeAnalytics from '@/components/atm/DowntimeAnalytics';

const AssetTrackingPage = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchQuery, setSearchQuery] = useState('');
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  const tabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <BarChart3 className="h-4 w-4" />,
      description: 'Overview of assets and key metrics'
    },
    {
      id: 'assets',
      label: 'Asset Registry',
      icon: <Settings className="h-4 w-4" />,
      description: 'Manage equipment inventory and details'
    },
    {
      id: 'maintenance',
      label: 'Maintenance',
      icon: <Wrench className="h-4 w-4" />,
      description: 'Schedule and track maintenance activities'
    },
    {
      id: 'iot',
      label: 'IoT Monitoring',
      icon: <Activity className="h-4 w-4" />,
      description: 'Real-time equipment monitoring'
    },
    {
      id: 'calibration',
      label: 'Calibration',
      icon: <Gauge className="h-4 w-4" />,
      description: 'Track calibration schedules and alerts'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <TrendingUp className="h-4 w-4" />,
      description: 'Downtime analysis and performance metrics'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <ATMDashboard />;
      case 'assets':
        return <AssetRegistry searchQuery={searchQuery} />;
      case 'maintenance':
        return <MaintenanceScheduling searchQuery={searchQuery} />;
      case 'iot':
        return <IoTMonitoring />;
      case 'calibration':
        return <CalibrationManagement searchQuery={searchQuery} />;
      case 'analytics':
        return <DowntimeAnalytics />;
      default:
        return <ATMDashboard />;
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Asset Tracking & Maintenance"
        description="Monitor equipment lifecycle, maintenance schedules, and real-time conditions"
      />

      {/* Application Switcher */}
      <ApplicationSwitcher />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6">
            {tabs.map((tab) => (
              <TabsTrigger 
                key={tab.id} 
                value={tab.id}
                className="flex items-center gap-2 text-xs lg:text-sm"
              >
                {tab.icon}
                <span className="hidden sm:inline">{tab.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Search and Actions */}
          <div className="flex items-center gap-4">
            {(activeTab === 'assets' || activeTab === 'maintenance' || activeTab === 'calibration') && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder={`Search ${activeTab}...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            )}
          </div>
        </div>

        {/* Tab Content */}
        <div className="min-h-[600px]">
          {renderTabContent()}
        </div>
      </Tabs>

      {/* Quick Stats Footer */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">Active Assets</p>
                <p className="text-2xl font-bold">--</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-sm font-medium">Pending Maintenance</p>
                <p className="text-2xl font-bold">--</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">Active Alerts</p>
                <p className="text-2xl font-bold">--</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">IoT Sensors</p>
                <p className="text-2xl font-bold">--</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AssetTrackingPage;
