import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ContentMode } from "@/types/curate";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import {
  Image,
  FileVideo,
  Video,
  Link,
  FileText,
  FileAudio,
  FileArchive,
  Code,
  Boxes,
  Gamepad2,
  ListChecks,
  FormInput,
  Upload,
  FileQuestion,
  CheckSquare,
  Star,
  Calendar,
  Clock,
  Timer,
  Phone,
  Hash,
  PenTool,
} from "lucide-react";

interface AddComponentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectComponent: (type: string) => void;
  activeMode: ContentMode;
}

const AddComponentDialog: React.FC<AddComponentDialogProps> = ({
  open,
  onOpenChange,
  onSelectComponent,
  activeMode,
}) => {
  const contentComponents = [
    {
      type: "image",
      icon: <Image className="h-5 w-5 text-blue-500" />,
      label: "Image",
      description: "Upload or select from library",
    },
    {
      type: "video",
      icon: <FileVideo className="h-5 w-5 text-blue-500" />,
      label: "Video",
      description: "Upload or select from library",
    },
    {
      type: "youtube",
      icon: <Video className="h-5 w-5 text-red-500" />,
      label: "YouTube",
      description: "Embed a YouTube video",
    },
    {
      type: "weblink",
      icon: <Link className="h-5 w-5 text-green-500" />,
      label: "Web Link",
      description: "Add a link to external content",
    },
    {
      type: "text",
      icon: <FileText className="h-5 w-5 text-purple-500" />,
      label: "Text",
      description: "Rich text editor",
    },
    {
      type: "audio",
      icon: <FileAudio className="h-5 w-5 text-amber-500" />,
      label: "Audio",
      description: "Upload or record audio",
    },
    {
      type: "attachment",
      icon: <FileArchive className="h-5 w-5 text-slate-500" />,
      label: "Attachment",
      description: "Upload any file type",
    },
    {
      type: "embed",
      icon: <Code className="h-5 w-5 text-indigo-500" />,
      label: "Embed",
      description: "Embed HTML or iframe code",
    },
    {
      type: "scorm",
      icon: <Boxes className="h-5 w-5 text-orange-500" />,
      label: "SCORM",
      description: "Upload SCORM package",
    },
    {
      type: "webgl",
      icon: <Gamepad2 className="h-5 w-5 text-cyan-500" />,
      label: "WebGL",
      description: "Interactive 3D content",
    },
  ];

  const formComponents = [
    {
      type: "mcq",
      icon: <ListChecks className="h-5 w-5 text-blue-500" />,
      label: "Multiple Choice",
      description: "Multiple choice questions",
    },
    {
      type: "textbox",
      icon: <FormInput className="h-5 w-5 text-green-500" />,
      label: "Text Input",
      description: "Single or multi-line text input",
    },
    {
      type: "feedback-image",
      icon: <Upload className="h-5 w-5 text-purple-500" />,
      label: "Image Upload",
      description: "Allow users to upload images",
    },
    {
      type: "option",
      icon: <FileQuestion className="h-5 w-5 text-amber-500" />,
      label: "Option",
      description: "Radio button options",
    },
    {
      type: "checkbox",
      icon: <CheckSquare className="h-5 w-5 text-indigo-500" />,
      label: "Checkbox",
      description: "Multiple selection checkboxes",
    },
    {
      type: "star",
      icon: <Star className="h-5 w-5 text-yellow-500" />,
      label: "Rating",
      description: "Star rating component",
    },
    {
      type: "date",
      icon: <Calendar className="h-5 w-5 text-red-500" />,
      label: "Date",
      description: "Date picker component",
    },
    {
      type: "time",
      icon: <Clock className="h-5 w-5 text-cyan-500" />,
      label: "Time",
      description: "Time picker component",
    },
    {
      type: "duration",
      icon: <Timer className="h-5 w-5 text-slate-500" />,
      label: "Duration",
      description: "Duration input component",
    },
    {
      type: "phone",
      icon: <Phone className="h-5 w-5 text-emerald-500" />,
      label: "Phone",
      description: "Phone number input",
    },
    {
      type: "alphanumeric",
      icon: <Hash className="h-5 w-5 text-violet-500" />,
      label: "Alphanumeric",
      description: "Custom pattern input",
    },
    {
      type: "sign",
      icon: <PenTool className="h-5 w-5 text-orange-500" />,
      label: "Signature",
      description: "Digital signature field",
    },
  ];

  const handleSelectComponent = (type: string) => {
    onSelectComponent(type);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Component</DialogTitle>
        </DialogHeader>
        <Tabs defaultValue={activeMode}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="communicate">Content</TabsTrigger>
            <TabsTrigger value="feedback">Form</TabsTrigger>
          </TabsList>
          <TabsContent value="communicate" className="mt-4">
            <ScrollArea className="h-[400px] pr-4">
              <div className="grid grid-cols-2 gap-3">
                {contentComponents.map((component) => (
                  <Button
                    key={component.type}
                    variant="outline"
                    className="h-auto p-4 justify-start flex-col items-start text-left"
                    onClick={() => handleSelectComponent(component.type)}
                  >
                    <div className="flex items-center mb-2">
                      {component.icon}
                      <span className="ml-2 font-medium">{component.label}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {component.description}
                    </p>
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
          <TabsContent value="feedback" className="mt-4">
            <ScrollArea className="h-[400px] pr-4">
              <div className="grid grid-cols-2 gap-3">
                {formComponents.map((component) => (
                  <Button
                    key={component.type}
                    variant="outline"
                    className="h-auto p-4 justify-start flex-col items-start text-left"
                    onClick={() => handleSelectComponent(component.type)}
                  >
                    <div className="flex items-center mb-2">
                      {component.icon}
                      <span className="ml-2 font-medium">{component.label}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {component.description}
                    </p>
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default AddComponentDialog;
