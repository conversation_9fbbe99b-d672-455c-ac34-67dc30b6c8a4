import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { fetchServices, Service } from '@/services/api';
import { cn } from '@/lib/utils';
import { ChevronDown, Layers, Check } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

interface ApplicationSwitcherProps {
  className?: string;
}

const ApplicationSwitcher: React.FC<ApplicationSwitcherProps> = ({ className }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { accessToken } = useSelector((state: RootState) => state.auth);
  
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentApplication, setCurrentApplication] = useState<Service | null>(null);

  // Load services on component mount
  useEffect(() => {
    const loadServices = async () => {
      if (!accessToken) {
        setError("Authentication token not found");
        setIsLoading(false);
        return;
      }

      try {
        const servicesData = await fetchServices(accessToken);
        setServices(servicesData);
        setIsLoading(false);
      } catch (err) {
        console.error("Error fetching services:", err);
        setError("Failed to load applications");
        setIsLoading(false);
      }
    };

    loadServices();
  }, [accessToken]);

  // Detect current application based on URL
  useEffect(() => {
    if (services.length === 0) return;

    // Find the service that matches the current URL path
    const foundService = services.find(service => {
      // Check if current path starts with the service URL
      return location.pathname === service.url ||
             (service.url.length > 1 && location.pathname.startsWith(service.url));
    });

    setCurrentApplication(foundService || null);
  }, [location.pathname, services]);

  // Handle application selection
  const handleApplicationSelect = (service: Service) => {
    navigate(service.url);
  };

  // Get display text for current application
  const getCurrentApplicationText = () => {
    if (currentApplication) {
      return currentApplication.name;
    }
    
    // Check if we're on a main page
    if (location.pathname === '/' || location.pathname === '/dashboard') {
      return 'Dashboard';
    }
    if (location.pathname === '/actions') {
      return 'My Actions';
    }
    
    return 'Select Application';
  };

  // Check if we should show the application switcher
  const shouldShowSwitcher = () => {
    // Only show on application pages (those with /apps/ routes)
    return location.pathname.startsWith('/apps/');
  };

  if (!shouldShowSwitcher()) {
    return null;
  }

  return (
    <div className={cn("flex items-center justify-between mb-4", className)}>
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <Layers className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium text-foreground">Application:</span>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="h-9 px-3 text-sm bg-card hover:bg-accent hover:text-accent-foreground border-border transition-all duration-200"
              disabled={isLoading}
            >
              <span className="max-w-48 truncate">
                {isLoading ? 'Loading...' : getCurrentApplicationText()}
              </span>
              <ChevronDown className="ml-2 h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
              
              <DropdownMenuContent align="start" className="w-64 bg-popover border-border shadow-lg">
                <DropdownMenuLabel className="text-foreground font-semibold">Switch Application</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-border" />

                {/* Applications Only */}
                {error ? (
                  <DropdownMenuItem disabled>
                    <span className="text-destructive text-sm">Error loading applications</span>
                  </DropdownMenuItem>
                ) : services.length === 0 ? (
                  <DropdownMenuItem disabled>
                    <span className="text-muted-foreground text-sm">No applications available</span>
                  </DropdownMenuItem>
                ) : (
                  services.map((service) => {
                    const isActive = location.pathname === service.url ||
                                   (service.url.length > 1 && location.pathname.startsWith(service.url));

                    return (
                      <DropdownMenuItem
                        key={service.id}
                        onClick={() => handleApplicationSelect(service)}
                        className={cn(
                          "flex items-center justify-between cursor-pointer transition-colors duration-200",
                          isActive
                            ? "bg-accent text-accent-foreground"
                            : "hover:bg-accent hover:text-accent-foreground"
                        )}
                      >
                        <span className="truncate">{service.name}</span>
                        {isActive && <Check className="h-4 w-4 ml-2 flex-shrink-0 text-primary" />}
                      </DropdownMenuItem>
                    );
                  })
                )}
              </DropdownMenuContent>
            </DropdownMenu>
      </div>

      {/* Optional: Show current application info on larger screens */}
      {currentApplication && (
        <div className="hidden lg:flex items-center text-sm text-muted-foreground">
          <span>{currentApplication.maskName || currentApplication.name}</span>
        </div>
      )}
    </div>
  );
};

export default ApplicationSwitcher;
