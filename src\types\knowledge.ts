import { ContentComponent } from './curate';

export interface KnowledgeUnitContentItem extends ContentComponent {
  // Inherits all properties from ContentComponent
  // Additional properties specific to knowledge units can be added here
}

export interface KnowledgeUnitResponse {
  id: string;
  name: string;
  description?: string;
  stepCount?: number;
  knowledgeTopicId: string;
  created?: string;
  updated?: string;
  isDefault?: boolean;
  value?: KnowledgeUnitContentItem[]; // Content items for the unit
}

export interface KnowledgeArea {
  id: string;
  name: string;
  description?: string;
  created?: string;
  updated?: string;
}

export interface KnowledgeTopic {
  id: string;
  name: string;
  areaId: string;
  description?: string;
  created?: string;
  updated?: string;
}

export interface KnowledgeUnit {
  id: string;
  name: string;
  topicId: string;
  description?: string;
  created?: string;
  updated?: string;
}

// Request/Response types for API operations
export interface CreateKnowledgeAreaRequest {
  name: string;
  description?: string;
}

export interface UpdateKnowledgeAreaRequest {
  name?: string;
  description?: string;
}

export interface CreateKnowledgeTopicRequest {
  name: string;
  areaId: string;
  description?: string;
}

export interface UpdateKnowledgeTopicRequest {
  name?: string;
  description?: string;
}

export interface CreateKnowledgeUnitRequest {
  name: string;
  topicId: string;
  description?: string;
}

export interface UpdateKnowledgeUnitRequest {
  name?: string;
  description?: string;
}

export interface UpdateKnowledgeUnitContentRequest {
  value: KnowledgeUnitContentItem[];
}
