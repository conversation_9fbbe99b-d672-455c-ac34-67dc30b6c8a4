import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { X, Check, Search, MapPin, RotateCcw } from 'lucide-react';
import apiService from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';
import { LocationFilterState } from '@/types/locationFilter';

interface LocationItem {
  id: string;
  name: string;
}

interface LocationTitles {
  tier1: string;
  tier2: string;
  tier3: string;
  tier4: string;
  tier5: string;
  tier6: string;
}

interface LocationFilterDropdownProps {
  onFilterChange: (filters: LocationFilterState) => void;
  className?: string;
}

// Custom SearchableSelect component
interface SearchableSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder: string;
  options: LocationItem[];
  label: string;
}

const SearchableSelect: React.FC<SearchableSelectProps> = ({
  value,
  onValueChange,
  placeholder,
  options,
  label
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const filteredOptions = options.filter(option =>
    option.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedOption = options.find(option => option.id === value);

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="relative">
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between h-10 px-3 py-2 text-sm border-gray-300 hover:border-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            >
              <span className={selectedOption ? "text-gray-900" : "text-gray-500"}>
                {selectedOption ? selectedOption.name : placeholder}
              </span>
              <Search className="h-4 w-4 text-gray-400" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-full p-0" align="start">
            <div className="p-2 border-b">
              <Input
                placeholder={`Search ${label.toLowerCase()}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-8 text-sm"
                autoFocus
              />
            </div>
            <div className="max-h-48 overflow-y-auto">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => (
                  <div
                    key={option.id}
                    className={`px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 ${
                      value === option.id ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
                    }`}
                    onClick={() => {
                      onValueChange(option.id);
                      setIsOpen(false);
                      setSearchTerm('');
                    }}
                  >
                    {option.name}
                  </div>
                ))
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  No {label.toLowerCase()} found
                </div>
              )}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

const LocationFilterDropdown: React.FC<LocationFilterDropdownProps> = ({
  onFilterChange,
  className = ''
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);

  // Location data
  const [locationOne, setLocationOne] = useState<LocationItem[]>([]);
  const [locationTwo, setLocationTwo] = useState<LocationItem[]>([]);
  const [locationThree, setLocationThree] = useState<LocationItem[]>([]);
  const [locationFour, setLocationFour] = useState<LocationItem[]>([]);
  const [locationFive, setLocationFive] = useState<LocationItem[]>([]);
  const [locationSix, setLocationSix] = useState<LocationItem[]>([]);

  // Selected values
  const [selectedLocationOne, setSelectedLocationOne] = useState<string>('');
  const [selectedLocationTwo, setSelectedLocationTwo] = useState<string>('');
  const [selectedLocationThree, setSelectedLocationThree] = useState<string>('');
  const [selectedLocationFour, setSelectedLocationFour] = useState<string>('');
  const [selectedLocationFive, setSelectedLocationFive] = useState<string>('');
  const [selectedLocationSix, setSelectedLocationSix] = useState<string>('');



  // Dynamic titles
  const [titles, setTitles] = useState<LocationTitles>({
    tier1: 'Country',
    tier2: 'Region',
    tier3: 'Site',
    tier4: 'Area',
    tier5: 'Unit',
    tier6: 'Sub-Unit'
  });

  // Fetch dynamic titles
  const fetchDynamicTitles = async () => {
    try {
      const response = await apiService.get('/dynamic-titles');
      if (response && response.length > 0) {
        const dynamicTitles = response.reduce((acc: any, item: any) => {
          acc[`tier${item.tier}`] = item.title;
          return acc;
        }, {});
        setTitles(prev => ({ ...prev, ...dynamicTitles }));
      }
    } catch (error) {
      console.error('Error fetching dynamic titles:', error);
    }
  };

  // Fetch location data
  const fetchLocationOne = async () => {
    try {
      const response = await apiService.get('/location-ones');
      setLocationOne(response || []);
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch locations",
        variant: "destructive",
      });
    }
  };

  const fetchLocationTwo = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-ones/${parentId}/location-twos`);
      setLocationTwo(response || []);
    } catch (error) {
      console.error('Error fetching location two:', error);
      setLocationTwo([]);
    }
  };

  const fetchLocationThree = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-twos/${parentId}/location-threes`);
      setLocationThree(response || []);
    } catch (error) {
      console.error('Error fetching location three:', error);
      setLocationThree([]);
    }
  };

  const fetchLocationFour = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-threes/${parentId}/location-fours`);
      setLocationFour(response || []);
    } catch (error) {
      console.error('Error fetching location four:', error);
      setLocationFour([]);
    }
  };

  const fetchLocationFive = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-fours/${parentId}/location-fives`);
      setLocationFive(response || []);
    } catch (error) {
      console.error('Error fetching location five:', error);
      setLocationFive([]);
    }
  };

  const fetchLocationSix = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-fives/${parentId}/location-sixes`);
      setLocationSix(response || []);
    } catch (error) {
      console.error('Error fetching location six:', error);
      setLocationSix([]);
    }
  };

  // Initialize data
  useEffect(() => {
    fetchDynamicTitles();
    fetchLocationOne();
  }, []);

  // Handle location changes
  const handleLocationOneChange = (value: string) => {
    setSelectedLocationOne(value);
    setSelectedLocationTwo('');
    setSelectedLocationThree('');
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationTwo([]);
    setLocationThree([]);
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationTwo(value);
    }
  };

  const handleLocationTwoChange = (value: string) => {
    setSelectedLocationTwo(value);
    setSelectedLocationThree('');
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationThree([]);
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationThree(value);
    }
  };

  const handleLocationThreeChange = (value: string) => {
    setSelectedLocationThree(value);
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationFour(value);
    }
  };

  const handleLocationFourChange = (value: string) => {
    setSelectedLocationFour(value);
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationFive(value);
    }
  };

  const handleLocationFiveChange = (value: string) => {
    setSelectedLocationFive(value);
    setSelectedLocationSix('');
    setLocationSix([]);
    
    if (value) {
      fetchLocationSix(value);
    }
  };

  const handleLocationSixChange = (value: string) => {
    setSelectedLocationSix(value);
  };

  // Apply filters
  const handleApplyFilter = () => {
    const filters: LocationFilterState = {
      locationOne: selectedLocationOne,
      locationTwo: selectedLocationTwo,
      locationThree: selectedLocationThree,
      locationFour: selectedLocationFour,
      locationFive: selectedLocationFive,
      locationSix: selectedLocationSix,
    };
    onFilterChange(filters);
    setIsOpen(false);
  };

  // Clear filters
  const handleClearFilter = () => {
    setSelectedLocationOne('');
    setSelectedLocationTwo('');
    setSelectedLocationThree('');
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationTwo([]);
    setLocationThree([]);
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    const filters: LocationFilterState = {
      locationOne: '',
      locationTwo: '',
      locationThree: '',
      locationFour: '',
      locationFive: '',
      locationSix: '',
    };
    onFilterChange(filters);
    setIsOpen(false);
  };

  // Check if any filter is applied
  const hasActiveFilters = selectedLocationOne || selectedLocationTwo || selectedLocationThree ||
                          selectedLocationFour || selectedLocationFive || selectedLocationSix;

  // Get selected location names for display
  const getSelectedLocationNames = () => {
    const names = [];
    if (selectedLocationOne) {
      const location = locationOne.find(l => l.id === selectedLocationOne);
      if (location) names.push(location.name);
    }
    if (selectedLocationTwo) {
      const location = locationTwo.find(l => l.id === selectedLocationTwo);
      if (location) names.push(location.name);
    }
    if (selectedLocationThree) {
      const location = locationThree.find(l => l.id === selectedLocationThree);
      if (location) names.push(location.name);
    }
    if (selectedLocationFour) {
      const location = locationFour.find(l => l.id === selectedLocationFour);
      if (location) names.push(location.name);
    }
    if (selectedLocationFive) {
      const location = locationFive.find(l => l.id === selectedLocationFive);
      if (location) names.push(location.name);
    }
    if (selectedLocationSix) {
      const location = locationSix.find(l => l.id === selectedLocationSix);
      if (location) names.push(location.name);
    }
    return names;
  };

  const selectedLocationNames = getSelectedLocationNames();
  const displayText = 'Location Filter';

  return (
    <div className="flex items-center justify-start space-x-4">
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`relative h-12 px-4 py-3 text-sm font-medium transition-all duration-200 ${
              hasActiveFilters
                ? 'bg-blue-50 border-blue-300 text-blue-700 hover:bg-blue-100 shadow-sm'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'
            } ${className}`}
            title={hasActiveFilters ? selectedLocationNames.join(' > ') : 'Click to set location filter'}
          >
            <MapPin className="h-4 w-4 mr-2" />
            <span className="font-medium">{displayText}</span>
            {hasActiveFilters && (
              <div className="absolute -top-1 -right-1 h-3 w-3 bg-blue-500 rounded-full border-2 border-white shadow-sm" />
            )}
          </Button>
        </DropdownMenuTrigger>
      <DropdownMenuContent className="w-96 p-0 shadow-xl border-gray-200" align="start">
        <div className="bg-white rounded-lg">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-blue-600" />
              <h4 className="font-semibold text-gray-900">Location Filter</h4>
            </div>
            <div className="flex items-center space-x-2">
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearFilter}
                  className="text-gray-500 hover:text-red-600 h-8 px-2"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
            {/* Location One */}
            <SearchableSelect
              value={selectedLocationOne}
              onValueChange={handleLocationOneChange}
              placeholder={`Select ${titles.tier1}`}
              options={locationOne}
              label={titles.tier1}
            />

            {/* Location Two */}
            {selectedLocationOne && locationTwo.length > 0 && (
              <SearchableSelect
                value={selectedLocationTwo}
                onValueChange={handleLocationTwoChange}
                placeholder={`Select ${titles.tier2}`}
                options={locationTwo}
                label={titles.tier2}
              />
            )}

            {/* Location Three */}
            {selectedLocationTwo && locationThree.length > 0 && (
              <SearchableSelect
                value={selectedLocationThree}
                onValueChange={handleLocationThreeChange}
                placeholder={`Select ${titles.tier3}`}
                options={locationThree}
                label={titles.tier3}
              />
            )}

            {/* Location Four */}
            {selectedLocationThree && locationFour.length > 0 && (
              <SearchableSelect
                value={selectedLocationFour}
                onValueChange={handleLocationFourChange}
                placeholder={`Select ${titles.tier4}`}
                options={locationFour}
                label={titles.tier4}
              />
            )}

            {/* Location Five */}
            {selectedLocationFour && locationFive.length > 0 && (
              <SearchableSelect
                value={selectedLocationFive}
                onValueChange={handleLocationFiveChange}
                placeholder={`Select ${titles.tier5}`}
                options={locationFive}
                label={titles.tier5}
              />
            )}

            {/* Location Six */}
            {selectedLocationFive && locationSix.length > 0 && (
              <SearchableSelect
                value={selectedLocationSix}
                onValueChange={handleLocationSixChange}
                placeholder={`Select ${titles.tier6}`}
                options={locationSix}
                label={titles.tier6}
              />
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-between items-center pt-4 border-t border-gray-200 bg-gray-50 px-4 py-3 rounded-b-lg">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilter}
              className="text-gray-600 hover:text-red-600 border-gray-300 hover:border-red-300"
              disabled={!hasActiveFilters}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
            <Button
              size="sm"
              onClick={handleApplyFilter}
              className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
            >
              <Check className="h-4 w-4 mr-2" />
              Apply Filter
            </Button>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>

    {/* Selected Location Path Display */}
    {hasActiveFilters && (
      <div className="flex items-center space-x-2 text-sm bg-blue-50 border border-blue-200 px-4 py-3 rounded-lg shadow-sm h-12">
        <MapPin className="h-4 w-4 text-blue-600 flex-shrink-0" />
        <div className="flex items-center space-x-2 flex-wrap">
          <span className="text-xs font-semibold text-blue-700 uppercase tracking-wide">Selected:</span>
          {selectedLocationNames.map((name, index) => (
            <React.Fragment key={index}>
              <span className="font-medium text-blue-900 bg-white px-2 py-1 rounded border border-blue-200">
                {name}
              </span>
              {index < selectedLocationNames.length - 1 && (
                <span className="text-blue-400 font-medium">→</span>
              )}
            </React.Fragment>
          ))}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClearFilter}
          className="ml-auto text-blue-600 hover:text-red-600 hover:bg-red-50 h-6 w-6 p-0"
          title="Clear filter"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    )}
    </div>
  );
};

export default LocationFilterDropdown;
