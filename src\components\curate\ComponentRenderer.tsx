import React, { useState } from "react";
import { DroppedItem, ContentComponent } from "@/types/curate";
import { Button } from "@/components/ui/button";
import { Edit, Trash2, GripVertical } from "lucide-react";
import { motion } from "framer-motion";
import ComponentPreview from "./ComponentPreview";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import ComponentEditor from "./ComponentEditor";

interface ComponentRendererProps {
  item: DroppedItem;
  index: number;
  onRemove: (id: string) => void;
  onUpdate: (id: string, data: ContentComponent) => void;
  onReorder?: (sourceId: string, targetId: string) => void;
  isDraggable?: boolean;
}

const ComponentRenderer: React.FC<ComponentRendererProps> = ({
  item,
  index,
  onRemove,
  onUpdate,
  onReorder,
  isDraggable = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;
    
    e.dataTransfer.setData("sourceId", item.id);
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;
    e.preventDefault();
    
    const sourceId = e.dataTransfer.getData("sourceId");
    if (sourceId && sourceId !== item.id) {
      onReorder(sourceId, item.id);
    }
    
    setIsDragging(false);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = (updatedData: ContentComponent) => {
    onUpdate(item.id, updatedData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleRemove = () => {
    onRemove(item.id);
  };

  // Get component type label for display
  const getComponentTypeLabel = () => {
    const typeMap: Record<string, string> = {
      image: "Image",
      video: "Video",
      youtube: "YouTube Video",
      weblink: "Web Link",
      text: "Text",
      audio: "Audio",
      attachment: "Attachment",
      embed: "Embed",
      scorm: "SCORM Package",
      webgl: "WebGL Content",
      mcq: "Multiple Choice Question",
      textbox: "Text Input",
      "feedback-image": "Image Upload",
      "feedback-video": "Video Upload",
      "feedback-audio": "Audio Upload",
      option: "Option",
      dropdown: "Dropdown",
      checkbox: "Checkbox",
      star: "Rating",
      date: "Date",
      time: "Time",
      duration: "Duration",
      phone: "Phone",
      alphanumeric: "Alphanumeric",
      sign: "Signature",
    };

    return typeMap[item.type] || item.type;
  };

  return (
    <>
      <motion.div
        className={`border rounded-md bg-white dark:bg-slate-800 shadow-sm overflow-hidden ${
          isDragging ? "opacity-50 border-dashed" : ""
        }`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        draggable={isDraggable}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onDragEnd={handleDragEnd}
      >
        <div className="flex items-center justify-between p-3 border-b bg-slate-50 dark:bg-slate-700">
          <div className="flex items-center">
            {isDraggable && (
              <div className="mr-2 cursor-grab text-muted-foreground">
                <GripVertical className="h-4 w-4" />
              </div>
            )}
            <span className="text-sm font-medium">{getComponentTypeLabel()}</span>
            <span className="ml-2 text-xs text-muted-foreground">#{index + 1}</span>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={handleEdit}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-destructive hover:text-destructive/90 hover:bg-destructive/10"
              onClick={handleRemove}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="p-4">
          <ComponentPreview item={item} />
        </div>
      </motion.div>

      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit {getComponentTypeLabel()}</DialogTitle>
          </DialogHeader>
          <ComponentEditor
            component={item.data}
            onSave={handleSave}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ComponentRenderer;
