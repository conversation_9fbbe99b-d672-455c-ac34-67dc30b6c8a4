import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ApplicationSwitcher from '@/components/layout/ApplicationSwitcher';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import { Badge } from '@/components/ui/badge';
import { format, parseISO, isAfter, isBefore, isSameDay, startOfDay } from 'date-fns';
import { Input } from '@/components/ui/input';
import {
  Search,
  Plus,
  MapPin,
  User,
  Edit,
  Settings,
  CheckCircle,
  Archive
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { MyInspectionAction, Inspection, ChecklistItem } from '@/types/inspection';
import {
  fetchInspectionActions,
  fetchScheduledInspections,
  fetchCompletedInspections,
  fetchChecklists,
  archiveChecklist,
  fetchServices,
  Service
} from '@/services/api';
import apiService from '@/services/apiService';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState } from '@/store';
import CreateInspectionModal from '@/components/inspections/CreateInspectionModal';
import EditInspectionModal from '@/components/inspections/EditInspectionModal';
import EditChecklistModal from '@/components/checklists/EditChecklistModal';
import AddChecklistModal from '@/components/checklists/AddChecklistModal';
import ChecklistDetailsModal from '@/components/checklists/ChecklistDetailsModal';
import ActionLogModal from '@/components/inspections/ActionLogModal';
import InspectionDetailsModal from '@/components/inspections/InspectionDetailsModal';
import ConductInspectionModal from '@/components/inspections/ConductInspectionModal';
import InspectionActionsModal from '@/components/inspections/InspectionActionsModal';
import { InspectionResponse, fetchInspectionWithDetails } from '@/services/api';
import DeleteConfirmationDialog from "@/components/users/DeleteConfirmationDialog";

// Function to format action type for display
const getActionTypeDisplay = (actionType: string): string => {
  switch (actionType) {
    case 'take_action':
      return 'Take Action';
    case 'verify_action':
      return 'Verify Action';
    case 'review':
      return 'Review Action';
    case 'reperform_action':
      return 'Re-perform Action';
    default:
      return actionType || 'Unknown';
  }
};

// Function to compute timeline status
const computeTimelineStatus = (scheduledDate: Date, dueDate?: Date): string => {
  const now = startOfDay(new Date());
  const start = startOfDay(scheduledDate);
  const due = dueDate ? startOfDay(dueDate) : start;

  if (isBefore(now, start)) return 'Upcoming';
  if ((isSameDay(now, start) || isAfter(now, start)) && (isSameDay(now, due) || isBefore(now, due))) return 'Due Now';
  if (isAfter(now, due)) return 'Overdue';
  return 'N/A';
};

// Function to render timeline status badge
const getTimelineStatusBadge = (status: string) => {
  const badgeStyle = { fontSize: '0.75rem', padding: '0.25rem 0.5rem' };

  switch (status) {
    case 'Upcoming':
      return <Badge style={badgeStyle} className="bg-blue-500 text-white">Upcoming</Badge>;
    case 'Due Now':
      return <Badge style={badgeStyle} className="bg-yellow-500 text-white">Due Now</Badge>;
    case 'Overdue':
      return <Badge style={badgeStyle} className="bg-red-500 text-white">Overdue</Badge>;
    default:
      return <Badge style={badgeStyle} className="bg-gray-500 text-white">N/A</Badge>;
  }
};

const InspectionPage = () => {
  const { toast } = useToast();
  const navigate = useNavigate();

  // Service name state
  const [serviceName, setServiceName] = useState<string>('Inspection');

  const [myActionsSearch, setMyActionsSearch] = useState('');
  const [scheduledInspectionsSearch, setScheduledInspectionsSearch] = useState('');
  const [checklistsSearch, setChecklistsSearch] = useState('');
  const [reportsSearch, setReportsSearch] = useState('');

  const [myActions, setMyActions] = useState<MyInspectionAction[]>([]);
  const [scheduledInspections, setScheduledInspections] = useState<Inspection[]>([]);
  const [checklists, setChecklists] = useState<ChecklistItem[]>([]);
  const [reports, setReports] = useState<Inspection[]>([]);

  // Counts for tabs
  const [actionCount, setActionCount] = useState(0);
  const [scheduledCount, setScheduledCount] = useState(0);
  const [checklistCount, setChecklistCount] = useState(0);
  const [reportCount, setReportCount] = useState(0);

  // Loading states
  const [loadingActions, setLoadingActions] = useState(false);
  const [loadingScheduled, setLoadingScheduled] = useState(false);
  const [loadingChecklists, setLoadingChecklists] = useState(false);
  const [loadingReports, setLoadingReports] = useState(false);

  // Create inspection modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Edit inspection modal state
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedInspection, setSelectedInspection] = useState<any>(null);

  // Checklist modal states
  const [isEditChecklistModalOpen, setIsEditChecklistModalOpen] = useState(false);
  const [isAddChecklistModalOpen, setIsAddChecklistModalOpen] = useState(false);
  const [selectedChecklist, setSelectedChecklist] = useState<any>(null);

  // Action log modal states
  const [isActionLogModalOpen, setIsActionLogModalOpen] = useState(false);
  const [selectedInspectionForLog, setSelectedInspectionForLog] = useState<any>(null);
  const [selectedActionData, setSelectedActionData] = useState<any[]>([]);

  // Inspection details modal states
  const [isInspectionDetailsModalOpen, setIsInspectionDetailsModalOpen] = useState(false);
  const [selectedInspectionDetails, setSelectedInspectionDetails] = useState<InspectionResponse | null>(null);

  // Conduct inspection modal states
  const [isConductInspectionModalOpen, setIsConductInspectionModalOpen] = useState(false);

  // Actions modal states
  const [isActionsModalOpen, setIsActionsModalOpen] = useState(false);
  const [selectedActionForModal, setSelectedActionForModal] = useState<any>(null);

  // Checklist details modal states
  const [isChecklistDetailsModalOpen, setIsChecklistDetailsModalOpen] = useState(false);
  const [selectedChecklistId, setSelectedChecklistId] = useState<string | null>(null);

  const { accessToken, user } = useSelector((state: RootState) => state.auth);

  // Debug: Log user roles for verification
  useEffect(() => {
    if (user && (user as any)?.roles) {
      console.log('User roles in InspectionPage:', (user as any).roles);
      console.log('Has ins_inspection_assignee role:', (user as any).roles.some((role: any) => ['ins_inspection_assignee'].includes(role.maskId)));
      console.log('Has ins_checklist_curator role:', (user as any).roles.some((role: any) => ['ins_checklist_curator'].includes(role.maskId)));
      console.log('Checklists tab will be', (user as any).roles.some((role: any) => ['ins_checklist_curator'].includes(role.maskId)) ? 'VISIBLE' : 'HIDDEN');
    }
  }, [user]);

  // Fetch service name based on current URL
  const fetchServiceName = useCallback(async () => {
    if (!accessToken) return;

    try {
      const services = await fetchServices(accessToken);
      // Find the service that matches the current URL path
      const currentPath = window.location.pathname;
      const foundService = services.find(service => {
        // Check if current path starts with the service URL
        return currentPath.startsWith(service.url);
      });

      if (foundService) {
        setServiceName(foundService.name);
      }
    } catch (error) {
      console.error('Error fetching service name:', error);
      // Keep default 'Inspection' if fetch fails
    }
  }, [accessToken]);

  // Fetch data functions
  const fetchMyActions = useCallback(async () => {
    if (!accessToken) return;
    setLoadingActions(true);
    try {
      const data = await fetchInspectionActions(accessToken);
      const enrichedActions = data.map(action => {
        const timelineStatus = action.dueDate ?
          (isBefore(startOfDay(new Date()), startOfDay(new Date(action.dueDate))) ? 'Upcoming' :
            isSameDay(startOfDay(new Date()), startOfDay(new Date(action.dueDate))) ? 'Due Now' : 'Overdue')
          : 'N/A';

        return {
          id: action.id,
          maskId: action.maskId || `ACT-${action.id}`,
          actionType: action.actionType || 'perform_task',
          actionToBeTaken: action.actionToBeTaken || action.description,
          location: 'Location', // Placeholder for location
          description: action.description,
          submittedBy: action.submittedBy?.firstName || 'Unknown',
          dueDate: action.dueDate || '',
          timelineStatus: timelineStatus,
          status: action.status,
          // Additional fields from API response
          application: action.application,
          applicationDetails: action.applicationDetails,
          applicationId: action.applicationId,
          trackId: action.trackId,
          uploads: action.uploads || [], // Array of uploaded files
          sequence: action.sequence,
          prefix: action.prefix,
          counter: action.counter,
          objectId: action.objectId,
          assignedToId: action.assignedToId,
          submitURL: action.submitURL,
          serviceId: action.serviceId,
          submittedById: action.submittedById,
          submittedByDetails: action.submittedBy, // Full submittedBy object with different name
          created: action.created,
          updated: action.updated
        };
      });

      setMyActions(enrichedActions);
      setActionCount(data.length);
    } catch (error) {
      console.error('Error fetching inspection actions:', error);
      toast({
        title: "Error",
        description: "Failed to fetch inspection actions",
        variant: "destructive"
      });
    } finally {
      setLoadingActions(false);
    }
  }, [accessToken, toast]);

  const fetchScheduledInspectionsData = useCallback(async () => {
    if (!accessToken) return;
    setLoadingScheduled(true);
    try {
      const data = await fetchScheduledInspections(accessToken);
      const enrichedInspections = data.map(inspection => {
        const scheduledDate = new Date(inspection.scheduledDate);
        // Use scheduledDate as fallback for dueDate if not available in API
        const dueDate = (inspection as any).dueDate ? new Date((inspection as any).dueDate) : scheduledDate;
        const timelineStatus = computeTimelineStatus(scheduledDate, dueDate);

        return {
          id: inspection.id,
          maskId: inspection.maskId,
          title: inspection.title,
          location: `${inspection.locationOne?.name || ''} - ${inspection.locationTwo?.name || ''}`,
          fullLocation: {
            country: inspection.locationOne?.name || '',
            region: inspection.locationTwo?.name || '',
            site: inspection.locationThree?.name || '',
            level: inspection.locationFour?.name || ''
          },
          status: inspection.status,
          scheduledDate: scheduledDate,
          dueDate: dueDate,
          timelineStatus: timelineStatus,
          description: inspection.description,
          checklist: inspection.checklist,
          inspector: inspection.inspector,
          assignedBy: inspection.assignedBy?.firstName || 'Unknown',
          // Location hierarchy
          locationOne: inspection.locationOne,
          locationTwo: inspection.locationTwo,
          locationThree: inspection.locationThree,
          locationFour: inspection.locationFour,
          locationFive: inspection.locationFive,
          locationSix: inspection.locationSix
        };
      });

      setScheduledInspections(enrichedInspections);
      setScheduledCount(data.length);
    } catch (error) {
      console.error('Error fetching scheduled inspections:', error);
      toast({
        title: "Error",
        description: "Failed to fetch scheduled inspections",
        variant: "destructive"
      });
    } finally {
      setLoadingScheduled(false);
    }
  }, [accessToken, toast]);

  const fetchChecklistsData = useCallback(async () => {
    if (!accessToken) return;
    setLoadingChecklists(true);
    try {
      const data = await fetchChecklists(accessToken);
      setChecklists(data.map(checklist => ({
        id: checklist.id,
        customId: checklist.customId,
        version: checklist.version,
        category: checklist.category,
        name: checklist.name,
        description: checklist.description,
        status: checklist.status,
        curator: checklist.curator?.firstName || 'Unknown',
        created: new Date(checklist.created),
        updated: new Date(checklist.updated)
      })));
      setChecklistCount(data.length);
    } catch (error) {
      console.error('Error fetching checklists:', error);
      toast({
        title: "Error",
        description: "Failed to fetch checklists",
        variant: "destructive"
      });
    } finally {
      setLoadingChecklists(false);
    }
  }, [accessToken, toast]);

  const fetchReportsData = useCallback(async () => {
    if (!accessToken) return;
    setLoadingReports(true);
    try {
      const data = await fetchCompletedInspections(accessToken);
      setReports(data.map(inspection => ({
        id: inspection.id,
        maskId: inspection.maskId,
        title: inspection.title,
        location: `${inspection.locationOne?.name || ''} - ${inspection.locationTwo?.name || ''}`,
        fullLocation: {
          country: inspection.locationOne?.name || '',
          region: inspection.locationTwo?.name || '',
          site: inspection.locationThree?.name || '',
          level: inspection.locationFour?.name || ''
        },
        status: inspection.status,
        scheduledDate: new Date(inspection.scheduledDate),
        completedDate: inspection.completedDate ? new Date(inspection.completedDate) : null,
        description: inspection.description,
        checklist: inspection.checklist,
        inspector: inspection.inspector,
        assignedBy: inspection.assignedBy?.firstName || 'Unknown',
        // Additional fields for reports (using available fields or placeholders)
        inspectionCategory: inspection.inspectionCategory, // Placeholder since not in API
        checklistVersion: inspection.checklistVersion, // Placeholder since not in API
        originalDueDate: new Date(inspection.dueDate), // Use scheduledDate as fallback
        actualCompletionDate: inspection.actualCompletionDate ? new Date(inspection.actualCompletionDate) : undefined,
        totalActions: 0, // Placeholder - would need to be calculated from actions
        completedActions: 0, // Placeholder - would need to be calculated from actions
        actionStatus: 'Pending', // Placeholder since not in API
        totalActionData: inspection.totalActions || [],
        // Location hierarchy
        locationOne: inspection.locationOne,
        locationTwo: inspection.locationTwo,
        locationThree: inspection.locationThree,
        locationFour: inspection.locationFour,
        locationFive: inspection.locationFive,
        locationSix: inspection.locationSix
      })));
      setReportCount(data.length);
    } catch (error) {
      console.error('Error fetching inspection reports:', error);
      toast({
        title: "Error",
        description: "Failed to fetch inspection reports",
        variant: "destructive"
      });
    } finally {
      setLoadingReports(false);
    }
  }, [accessToken, toast]);

  // Handle inspection creation success
  const handleInspectionCreated = useCallback(() => {
    // Refresh the scheduled inspections data
    fetchScheduledInspectionsData();
    setIsCreateModalOpen(false);
    toast({
      title: "Success",
      description: "Inspection created successfully",
    });
  }, [fetchScheduledInspectionsData, toast]);

  // Handle inspection edit
  const handleEditInspection = useCallback((inspection: any) => {
    console.log('Edit inspection clicked:', inspection);
    setSelectedInspection(inspection);
    setIsEditModalOpen(true);
  }, []);

  // Handle inspection update success
  const handleInspectionUpdated = useCallback(() => {
    // Refresh the scheduled inspections data
    fetchScheduledInspectionsData();
    setIsEditModalOpen(false);
    setSelectedInspection(null);
  }, [fetchScheduledInspectionsData]);

  // Load data on component mount
  useEffect(() => {
    if (accessToken) {
      fetchServiceName();
      fetchMyActions();
      fetchScheduledInspectionsData();
      fetchChecklistsData();
      fetchReportsData();
    }
  }, [accessToken, fetchServiceName, fetchMyActions, fetchScheduledInspectionsData, fetchChecklistsData, fetchReportsData]);

  // Filter functions
  const filteredMyActions = myActions.filter(action =>
    (action.description || '').toLowerCase().includes(myActionsSearch.toLowerCase()) ||
    (action.submittedBy || '').toLowerCase().includes(myActionsSearch.toLowerCase())
  );

  const filteredScheduledInspections = scheduledInspections.filter(inspection =>
    (inspection.title || '').toLowerCase().includes(scheduledInspectionsSearch.toLowerCase()) ||
    (inspection.location || '').toLowerCase().includes(scheduledInspectionsSearch.toLowerCase())
  );

  const filteredChecklists = checklists.filter(checklist =>
    (checklist.name || '').toLowerCase().includes(checklistsSearch.toLowerCase()) ||
    (checklist.description || '').toLowerCase().includes(checklistsSearch.toLowerCase())
  );

  const filteredReports = reports.filter(report =>
    (report.title || '').toLowerCase().includes(reportsSearch.toLowerCase()) ||
    (report.location || '').toLowerCase().includes(reportsSearch.toLowerCase())
  );

  // Helper functions for actions
  const getActionTimelineStatus = (dueDate: string) => {
    const badgeStyle = { fontSize: '0.75rem', padding: '0.25rem 0.5rem' };

    if (!dueDate) {
      return <Badge style={badgeStyle} className="bg-gray-500 text-white">N/A</Badge>;
    }

    const now = startOfDay(new Date());
    const due = startOfDay(new Date(dueDate));

    if (isBefore(now, due)) {
      return <Badge style={badgeStyle} className="bg-blue-500 text-white">Upcoming</Badge>;
    } else if (isSameDay(now, due)) {
      return <Badge style={badgeStyle} className="bg-yellow-500 text-white">Due Now</Badge>;
    } else if (isAfter(now, due)) {
      return <Badge style={badgeStyle} className="bg-red-500 text-white">Overdue</Badge>;
    }

    return <Badge style={badgeStyle} className="bg-gray-500 text-white">N/A</Badge>;
  };

  const getActionTypeDisplay = (actionType: string, actionToBeTaken?: string): string => {
    if (actionType === "perform_task") {
      return 'Take Action';
    } else if (actionType === "verify_task") {
      return 'Verify Action';
    } else if (actionType === "reperform_task") {
      return 'Retake Action';
    } else {
      return actionToBeTaken || actionType || 'Unknown Action';
    }
  };

  const formatDueDate = (dueDate: string): string => {
    if (!dueDate) return 'N/A';
    try {
      return format(new Date(dueDate), 'dd-MM-yyyy');
    } catch {
      return 'Invalid date';
    }
  };

  // Handle conduct inspection action
  const handleConductInspection = useCallback(async (action: any) => {
    console.log(action)
    if (!accessToken) {
      toast({
        title: "Authentication Error",
        description: "Please log in to conduct inspection.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Fetch detailed inspection data similar to action.js
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "checklist" },
          { relation: "inspector" },
          { relation: 'assignedBy' }
        ]
      };
      const url = `/inspections/${action.applicationId}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

      const inspectionDetails = await apiService.get(url);
      setSelectedInspectionDetails(inspectionDetails);

      // For ActionLogModal, we need to process the totalActions data
      const processedActionData = groupByDescription(inspectionDetails.totalActions || []);
      setSelectedActionData(processedActionData);

      // Check action type and open appropriate modal
      if (action.actionType === 'conduct_inspection') {
        // For conduct inspection, pass the single action to the modal
        setSelectedActionForModal(action);
        setIsConductInspectionModalOpen(true);
      } else {
        // For other action types (perform_task, verify_task, reperform_task, review)
        setSelectedActionForModal(action);
        setIsActionsModalOpen(true);
      }
    } catch (error) {
      console.error('Error fetching inspection details for conduct:', error);
      toast({
        title: "Error",
        description: "Failed to load inspection details. Please try again.",
        variant: "destructive"
      });
    }
  }, [accessToken, toast]);

  // Column definitions for tables
  const myActionsColumns = [
    {
      key: 'maskId',
      header: 'ID',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string, row: any) => (
        <div
          className="font-mono text-sm text-blue-600 cursor-pointer hover:underline"
          onClick={() => {
            // Handle all action types
            handleConductInspection(row);
          }}
        >
          {value ? (
            row.actionType === 'conduct_inspection'
              ? value
              : `${value}-IA-${(row.counter || 0) + 1}`
          ) : 'N/A'}
        </div>
      ),
    },
    {
      key: 'actionToBeTaken',
      header: 'Required Action',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string, row: any) => (
        <div className="font-medium">
          {getActionTypeDisplay(row.actionType, value)}
        </div>
      ),
    },
    {
      key: 'dueDate',
      header: 'Due Date',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string) => (
        <div className="text-sm">{formatDueDate(value)}</div>
      ),
    },
    {
      key: 'timelineStatus',
      header: 'Timeline',
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'Upcoming', value: 'Upcoming' },
        { label: 'Due Now', value: 'Due Now' },
        { label: 'Overdue', value: 'Overdue' }
      ],
      render: (value: string, row: any) => getActionTimelineStatus(row.dueDate),
    },
  ];

  const scheduledInspectionsColumns = [
    {
      key: 'maskId',
      header: `${serviceName} ID`,
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string) => (
        <div className="font-mono text-sm">{value || 'N/A'}</div>
      ),
    },
    {
      key: 'checklist',
      header: 'Checklist',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: any) => (
        <div
          className="text-sm text-blue-600 cursor-pointer hover:underline"
          onClick={() => {
            if (value?.id) {
              setSelectedChecklistId(value.id);
              setIsChecklistDetailsModalOpen(true);
            }
          }}
        >
          {value?.name || 'No checklist'}
        </div>
      ),
    },
    {
      key: 'inspector',
      header: 'Inspector',
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'John Doe', value: 'John Doe' },
        { label: 'Jane Smith', value: 'Jane Smith' },
        { label: 'Mike Johnson', value: 'Mike Johnson' }
      ],
      render: (value: any) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{value?.firstName || 'Unknown'}</span>
        </div>
      ),
    },
    {
      key: 'scheduledDate',
      header: 'Scheduled Date',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: Date) => {
        try {
          return value ? format(value, 'dd-MM-yyyy') : 'No date';
        } catch {
          return 'Invalid date';
        }
      },
    },
    {
      key: 'dueDate',
      header: 'Due Date',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: Date) => {
        try {
          return value ? format(value, 'dd-MM-yyyy') : 'No date';
        } catch {
          return 'Invalid date';
        }
      },
    },
    {
      key: 'timelineStatus',
      header: 'Timeline',
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'Upcoming', value: 'Upcoming' },
        { label: 'Due Now', value: 'Due Now' },
        { label: 'Overdue', value: 'Overdue' }
      ],
      render: (value: string) => getTimelineStatusBadge(value),
    },
    {
      key: 'actions',
      header: 'Actions',
      sortable: false,
      filterable: false,
      render: (value: any, row: any) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditInspection(row)}
            className="flex items-center gap-1"
          >
            <Edit className="h-3 w-3" />
            Edit
          </Button>
        </div>
      ),
    },
  ];

  // Helper functions for checklists
  const handleChecklistView = (row: any) => {
    console.log('View checklist:', row);
    setSelectedChecklistId(row.id);
    setIsChecklistDetailsModalOpen(true);
  };

  const handlePublish = (row: any) => {
    console.log('Publish checklist:', row);
    toast({
      title: "Publish Checklist",
      description: `Publishing checklist ${row.customId}`,
    });
  };

  const handleCurate = (row: any) => {
    console.log('Curate checklist:', row);
    // Navigate to the checklist curation page
    navigate(`/checklists/${row.id}/curate`);
  };

  const handleEdit = (row: any) => {
    console.log('Edit checklist:', row);
    setSelectedChecklist(row);
    setIsEditChecklistModalOpen(true);
  };

  const handleDelete = (row: any) => {
    setChecklistToDelete(row);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!accessToken || !checklistToDelete) return;
    try {
      await apiService.delete(`/checklists/${checklistToDelete.id}`);
      await fetchChecklistsData();
      toast({
        title: "Checklist Deleted",
        description: `Checklist ${checklistToDelete.customId} has been deleted permanently.`,
        variant: "destructive"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete checklist. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setChecklistToDelete(null);
    }
  };

  const checklistsColumns = [
    {
      key: 'customId',
      header: 'Checklist ID',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string, row: any) => (
        <div
          className="font-mono text-sm text-blue-600 cursor-pointer hover:underline"
          onClick={() => handleChecklistView(row)}
        >
          {value || 'N/A'}
        </div>
      ),
    },
    {
      key: 'version',
      header: 'Version',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string) => (
        <div className="text-sm">{value || 'N/A'}</div>
      ),
    },
    {
      key: 'name',
      header: 'Checklist Name',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string) => (
        <div className="font-medium">{value || 'No name'}</div>
      ),
    },
    {
      key: 'category',
      header: 'Category',
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'Safety', value: 'Safety' },
        { label: 'Quality', value: 'Quality' },
        { label: 'Environmental', value: 'Environmental' },
        { label: 'Maintenance', value: 'Maintenance' }
      ],
      render: (value: string) => (
        <div className="text-sm">{value || 'No category'}</div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'Active', value: 'Active' },
        { label: 'Draft', value: 'Draft' },
        { label: 'Published', value: 'Published' },
        { label: 'Archived', value: 'Archived' }
      ],
      render: (value: string) => {
        const getStatusBadge = (status: string) => {
          switch (status?.toLowerCase()) {
            case 'active':
              return <Badge className="bg-green-500 text-white">{status}</Badge>;
            case 'published':
              return <Badge className="bg-blue-500 text-white">{status}</Badge>;
            case 'draft':
              return <Badge className="bg-yellow-500 text-white">{status}</Badge>;
            case 'archived':
              return <Badge className="bg-red-500 text-white">{status}</Badge>;
            default:
              return <Badge variant="secondary">{status || 'Unknown'}</Badge>;
          }
        };
        return getStatusBadge(value);
      },
    },
    {
      key: 'curator',
      header: 'Checklist Curator',
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'John Doe', value: 'John Doe' },
        { label: 'Jane Smith', value: 'Jane Smith' },
        { label: 'Mike Johnson', value: 'Mike Johnson' }
      ],
      render: (value: any) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{value?.firstName || value || 'Unknown'}</span>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      sortable: false,
      filterable: false,
      render: (value: any, row: any) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row)}
            className="flex items-center gap-1"
          >
            <Edit className="h-3 w-3" />
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCurate(row)}
            className="flex items-center gap-1"
          >
            <Settings className="h-3 w-3" />
            Curate
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDelete(row)}
            className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Archive className="h-3 w-3" />
            Delete
          </Button>
        </div>
      ),
    },
  ];

  // Helper functions for reports
  const groupByDescription = (data: any[]) => {
    const filterData = data.filter(item =>
      item.actionType !== 'review_incident' &&
      item.actionType !== 'conduct_inspection' &&
      item.actionType !== 'approve_investigation'
    );

    const groupedData = [];
    const descriptionMap: any = {};

    filterData.forEach(item => {
      const { objectId, description, actionType, assignedToId, status, trackId } = item;
      if (!descriptionMap[trackId]) {
        descriptionMap[trackId] = {
          objectId: objectId,
          firstActionType: actionType,
          lastActionType: actionType,
          actionTypes: [actionType],
          lastAssignedToId: assignedToId,
          lastStatus: status,
          data: []
        };
      } else {
        descriptionMap[trackId].lastActionType = actionType;
        descriptionMap[trackId].actionTypes.push(actionType);
        descriptionMap[trackId].lastAssignedToId = assignedToId;
        descriptionMap[trackId].lastStatus = status;
      }
      descriptionMap[trackId].data.push(item);
    });

    // Update lastActionType, lastAssignedToId, and lastStatus in each group
    for (const description in descriptionMap) {
      const group = descriptionMap[description];
      const lastDataObject = group.data[group.data.length - 1];
      group.lastActionType = lastDataObject.actionType;
      group.lastAssignedToId = lastDataObject.assignedToId;
      group.lastStatus = lastDataObject.status;
      groupedData.push(group);
    }

    return groupedData;
  };

  const locationBodyTemplate = (row: any) => {
    const parts = [
      row?.locationOne?.name,
      row?.locationTwo?.name,
      row?.locationThree?.name,
      row?.locationFour?.name
    ].filter(Boolean);
    return parts.join(' > ');
  };

  const outcomeTemplate = (status: string) => {
    if (status === 'Completed with Actions') {
      return <Badge className="bg-yellow-500 text-white">{status}</Badge>;
    }
    if (status === 'Completed without Actions') {
      return <Badge className="bg-green-500 text-white">{status}</Badge>;
    }
    if (status === 'Archived without Completion') {
      return <Badge className="bg-red-500 text-white">{status}</Badge>;
    }
    return <Badge variant="secondary">{status}</Badge>;
  };

  const actionStatusTemplate = (rowData: any) => {
    // Use the actual totalActions from the inspection data, or fallback to empty array
    const totalActionData = groupByDescription(rowData.totalActionData || []);

    console.log(totalActionData);
    console.log(rowData.maskId);

    const totalCompleted = totalActionData.filter(item =>
      item.lastActionType === 'verify_task' && item.lastStatus === 'Completed'
    );

    const colorClass = totalActionData.length === totalCompleted.length ? 'text-green-600' :
      totalCompleted.length === 0 ? 'text-red-600' : 'text-orange-600';

    const handleActionLog = (rowData: any, totalActionData: any[]) => {
      // Open action log modal
      setSelectedInspectionForLog(rowData);
      setSelectedActionData(totalActionData);
      setIsActionLogModalOpen(true);
    };

    return (
      <button
        onClick={(e) => {
          e.preventDefault();
          handleActionLog(rowData, totalActionData);
        }}
        className={`font-medium hover:underline ${colorClass}`}
      >
        {totalCompleted.length} / {totalActionData.length}
      </button>
    );
  };

  const handleView = async (row: any) => {
    if (!accessToken) {
      toast({
        title: "Authentication Error",
        description: "Please log in to view inspection details.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Fetch detailed inspection data
      const inspectionDetails = await fetchInspectionWithDetails(row.id, accessToken);
      setSelectedInspectionDetails(inspectionDetails);
      setIsInspectionDetailsModalOpen(true);
    } catch (error) {
      console.error('Error fetching inspection details:', error);
      toast({
        title: "Error",
        description: "Failed to load inspection details. Please try again.",
        variant: "destructive"
      });
    }
  };

  const reportsColumns = [
    {
      key: 'maskId',
      header: 'Inspection ID',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string, row: any) => (
        <div
          className="font-mono text-sm text-blue-600 cursor-pointer hover:underline"
          onClick={() => handleView(row)}
        >
          {value || 'N/A'}
        </div>
      ),
    },
    {
      key: 'inspectionCategory',
      header: 'Inspection Category',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string) => (
        <div className="text-sm">{value || 'No category'}</div>
      ),
    },
    {
      key: 'checklist',
      header: 'Inspection Checklist',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: any) => (
        <div
          className="text-sm text-blue-600 cursor-pointer hover:underline"
          onClick={() => {
            if (value?.id) {
              setSelectedChecklistId(value.id);
              setIsChecklistDetailsModalOpen(true);
            }
          }}
        >
          {value?.name || 'No checklist'}
        </div>
      ),
    },
    {
      key: 'checklistVersion',
      header: 'Checklist Version',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string) => (
        <div className="text-sm">{value || 'N/A'}</div>
      ),
    },
    {
      key: 'inspector',
      header: 'Assigned Inspector',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: any) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{value?.firstName || 'Unknown'}</span>
        </div>
      ),
    },
    {
      key: 'location',
      header: 'Location',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string, row: any) => (
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{locationBodyTemplate(row)}</span>
        </div>
      ),
    },
    {
      key: 'originalDueDate',
      header: 'Original Due Date',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string | Date) => {
        try {
          const date = typeof value === 'string' ? new Date(value) : value;
          return date ? format(date, 'dd-MM-yyyy') : 'No date';
        } catch {
          return 'Invalid date';
        }
      },
    },
    {
      key: 'actualCompletionDate',
      header: 'Actual Completion Date',
      sortable: true,
      filterable: true,
      filterType: 'text' as const,
      render: (value: string | Date) => {
        try {
          const date = typeof value === 'string' ? new Date(value) : value;
          return date ? format(date, 'dd-MM-yyyy') : 'Not completed';
        } catch {
          return 'Invalid date';
        }
      },
    },
    {
      key: 'status',
      header: 'Inspection Outcome',
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'Completed with Actions', value: 'Completed with Actions' },
        { label: 'Completed without Actions', value: 'Completed without Actions' },
        { label: 'Archived without Completion', value: 'Archived without Completion' },
        { label: 'In Progress', value: 'In Progress' },
        { label: 'Pending', value: 'Pending' }
      ],
      render: (value: string) => outcomeTemplate(value || 'Unknown'),
    },
    {
      key: 'actionStatus',
      header: 'Status of Actions',
      sortable: false,
      filterable: false,
      render: (value: any, row: any) => actionStatusTemplate(row),
    },
  ];

  const allTabs = [
    {
      value: "my-actions",
      label: `My Actions (${actionCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search actions..."
                value={myActionsSearch}
                onChange={(e) => setMyActionsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <ExpandableDataTable
            data={filteredMyActions}
            columns={myActionsColumns}
          />
        </div>
      )
    },
    // Checklists tab - conditionally included
    ...(user && (user as any)?.roles?.some((role: any) => ['ins_checklist_curator'].includes(role.maskId)) ? [{
      value: "checklists",
      label: `Checklists (${checklistCount})`,
      content: (
        <div className="space-y-4">
  <div className="flex items-center justify-between gap-4">
    <div className="relative flex-1 max-w-md">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
      <Input
        placeholder="Search checklists..."
        value={checklistsSearch}
        onChange={(e) => setChecklistsSearch(e.target.value)}
        className="pl-10"
      />
    </div>
    <Button
      className="flex items-center gap-1"
      onClick={() => setIsAddChecklistModalOpen(true)}
    >
      <Plus className="h-4 w-4" /> Add Checklist
    </Button>
  </div>

  <ExpandableDataTable
    data={filteredChecklists}
    columns={checklistsColumns}
  />
</div>

      )
    }] : []),
    {
      value: "scheduled-inspections",
      label: `Scheduled ${serviceName} (${scheduledCount})`,
      content: (
        <div className="space-y-4">
  <div className="flex justify-between items-center gap-4">
    <div className="relative flex-1 max-w-md">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
      <Input
        placeholder="Search scheduled inspections..."
        value={scheduledInspectionsSearch}
        onChange={(e) => setScheduledInspectionsSearch(e.target.value)}
        className="pl-10"
      />
    </div>
    
    {user && (user as any)?.roles?.some((role: any) => ['ins_inspection_assignee'].includes(role.maskId)) && (
      <Button
        className="flex items-center gap-1"
        onClick={() => setIsCreateModalOpen(true)}
      >
        <Plus className="h-4 w-4" /> Schedule {serviceName}
      </Button>
    )}
  </div>

  <ExpandableDataTable
    data={filteredScheduledInspections}
    columns={scheduledInspectionsColumns}
  />
</div>

      )
    },
    {
      value: "reports",
      label: `Reports (${reportCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search reports..."
                value={reportsSearch}
                onChange={(e) => setReportsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <ExpandableDataTable
            data={filteredReports}
            columns={reportsColumns}
          />
        </div>
      )
    }
  ];

  const tabs = allTabs;

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [checklistToDelete, setChecklistToDelete] = useState<any>(null);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageHeader
          title={serviceName}
          description="Set up checklists and scheduling for consistent inspections and compliance tracking"
        />
      </div>

      {/* Application Switcher */}
      <ApplicationSwitcher />

      <TabsContainer tabs={tabs} defaultValue="my-actions" />

      <CreateInspectionModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleInspectionCreated}
        accessToken={accessToken}
      />

      <EditInspectionModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSuccess={handleInspectionUpdated}
        accessToken={accessToken}
        inspection={selectedInspection}
      />

      <EditChecklistModal
        isOpen={isEditChecklistModalOpen}
        onClose={() => setIsEditChecklistModalOpen(false)}
        onSuccess={() => {
          fetchChecklistsData();
          setIsEditChecklistModalOpen(false);
          setSelectedChecklist(null);
        }}
        accessToken={accessToken}
        checklist={selectedChecklist}
      />

      <AddChecklistModal
        isOpen={isAddChecklistModalOpen}
        onClose={() => setIsAddChecklistModalOpen(false)}
        onSuccess={() => {
          fetchChecklistsData();
          setIsAddChecklistModalOpen(false);
        }}
        accessToken={accessToken}
      />

      <ActionLogModal
        open={isActionLogModalOpen}
        onOpenChange={setIsActionLogModalOpen}
        inspectionData={selectedInspectionForLog}
        totalActionData={selectedActionData}
      />

      <InspectionDetailsModal
        open={isInspectionDetailsModalOpen}
        onOpenChange={setIsInspectionDetailsModalOpen}
        inspection={selectedInspectionDetails}
      />

      <ConductInspectionModal
        isOpen={isConductInspectionModalOpen}
        onClose={() => setIsConductInspectionModalOpen(false)}
        onSuccess={() => {
          fetchMyActions();
          setIsConductInspectionModalOpen(false);
        }}
        inspectionDetails={selectedInspectionDetails}
        actionData={selectedActionForModal}
      />

      <InspectionActionsModal
        isOpen={isActionsModalOpen}
        onClose={() => {
          setIsActionsModalOpen(false);
          setSelectedActionForModal(null);
        }}
        onSuccess={() => {
          fetchMyActions();
          setIsActionsModalOpen(false);
          setSelectedActionForModal(null);
        }}
        inspectionDetails={selectedInspectionDetails}
        actionData={selectedActionForModal}
      />

      <ChecklistDetailsModal
        open={isChecklistDetailsModalOpen}
        onOpenChange={setIsChecklistDetailsModalOpen}
        checklistId={selectedChecklistId}
      />

      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={confirmDelete}
        title="Delete Checklist"
        description={`Are you sure you want to permanently delete the checklist ${checklistToDelete?.customId || ''}? This action cannot be undone.`}
        user={checklistToDelete}
      />
    </div>
  );
};

export default InspectionPage;
