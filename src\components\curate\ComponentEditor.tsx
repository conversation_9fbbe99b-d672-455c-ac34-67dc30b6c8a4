import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ContentComponent } from "@/types/curate";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";
import { v4 as uuidv4 } from "uuid";
import { PlusCircle, Trash2, Link, FileAudio, FileArchive, Boxes, Gamepad2, Upload, CheckSquare, Star, Calendar, Clock, Timer, Phone, Hash, PenTool, FormInput, FileQuestion, Loader2 } from "lucide-react";
import { useFileUpload, useImageUpload, useVideoUpload, useAudioUpload } from "@/hooks/useFileUpload";
import { formatFileSize } from "@/utils/fileUtils";
import { useImagePresignedUrl, useVideoPresignedUrl, useAttachmentPresignedUrl } from "@/hooks/usePresignedUrl";

interface ComponentEditorProps {
  component: ContentComponent;
  onSave: (data: ContentComponent) => void;
  onCancel: () => void;
}

const ComponentEditor: React.FC<ComponentEditorProps> = ({
  component,
  onSave,
  onCancel,
}) => {
  const [editedComponent, setEditedComponent] = useState<ContentComponent>({ ...component });
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get presigned URLs for file previews
  const imageUrl = useImagePresignedUrl(editedComponent);
  const videoUrl = useVideoPresignedUrl(editedComponent);
  const attachmentUrl = useAttachmentPresignedUrl(editedComponent);

  // File upload hooks for different file types
  const imageUpload = useImageUpload({
    onUploadSuccess: (fileInfo) => handleFileUploadSuccess(fileInfo, 'image')
  });

  const videoUpload = useVideoUpload({
    onUploadSuccess: (fileInfo) => handleFileUploadSuccess(fileInfo, 'video')
  });

  const audioUpload = useAudioUpload({
    onUploadSuccess: (fileInfo) => handleFileUploadSuccess(fileInfo, 'audio')
  });

  const fileUpload = useFileUpload({
    onUploadSuccess: (fileInfo) => handleFileUploadSuccess(fileInfo, 'file')
  });

  // File size limits in bytes
  const FILE_SIZE_LIMITS = {
    image: 20 * 1024 * 1024, // 20MB
    video: 150 * 1024 * 1024, // 150MB
    audio: 50 * 1024 * 1024, // 50MB
    attachment: 50 * 1024 * 1024, // 50MB
    scorm: 100 * 1024 * 1024, // 100MB for SCORM packages
    webgl: 100 * 1024 * 1024, // 100MB for WebGL files
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    // Special handling for YouTube URL to extract video ID
    if (name === 'url' && component.type === 'youtube') {
      const videoId = extractYouTubeId(value);
      setEditedComponent((prev) => ({
        ...prev,
        [name]: value,
        videoId: videoId
      }));
    } else {
      setEditedComponent((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setEditedComponent((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle successful file upload
  const handleFileUploadSuccess = async (fileInfo: any, fileType: string) => {
    try {
      // Update the component data with the uploaded file information
      // Don't store presigned URL - let the preview components fetch fresh URLs
      setEditedComponent((prev) => ({
        ...prev,
        // Remove any existing src to force fresh presigned URL fetch
        src: undefined,
        // Store file information for presigned URL fetching
        fileName: fileInfo.fileName, // This is the server file name for API calls
        originalFileName: fileInfo.fileName, // Store the server file name for API calls
        fileSize: fileInfo.size,
        // Store original name for display purposes
        originalName: fileInfo.originalName,
      }));



    } catch (error) {
      console.error('Error in file upload success handler:', error);
      toast({
        title: "Error",
        description: "Failed to process uploaded file.",
        variant: "destructive",
      });
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const file = e.target.files[0];
    const componentType = component.type;

    // Determine which upload hook to use based on component type
    try {
      if (componentType === 'image' || componentType === 'feedback-image') {
        await imageUpload.handleFileUpload(file);
      } else if (componentType === 'video' || componentType === 'feedback-video') {
        await videoUpload.handleFileUpload(file);
      } else if (componentType === 'audio' || componentType === 'feedback-audio') {
        await audioUpload.handleFileUpload(file);
      } else {
        // For other file types (scorm, webgl, etc.)
        await fileUpload.handleFileUpload(file);
      }
    } catch (error) {
      console.error('File upload failed:', error);
      // Error is already handled by the upload hook
    }

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleAddOption = () => {
    if (!('options' in editedComponent)) return;

    const newOption = { id: uuidv4(), text: `Option ${(editedComponent.options?.length || 0) + 1}` };

    setEditedComponent((prev) => ({
      ...prev,
      options: [...(prev.options || []), newOption],
    }));
  };

  const handleRemoveOption = (optionId: string) => {
    if (!('options' in editedComponent)) return;

    setEditedComponent((prev) => ({
      ...prev,
      options: prev.options?.filter(option => option.id !== optionId) || [],
    }));
  };

  const handleOptionChange = (optionId: string, value: string) => {
    if (!('options' in editedComponent)) return;

    setEditedComponent((prev) => ({
      ...prev,
      options: prev.options?.map(option =>
        option.id === optionId ? { ...option, text: value } : option
      ) || [],
    }));
  };

  // Extract YouTube video ID from URL
  const extractYouTubeId = (url: string): string => {
    if (!url) return '';

    // Match patterns like:
    // https://www.youtube.com/watch?v=VIDEO_ID
    // https://youtu.be/VIDEO_ID
    // https://www.youtube.com/embed/VIDEO_ID
    const regExp = /^.*(youtu.be\/|v\/|e\/|u\/\w+\/|embed\/|v=)([^#\&\?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : '';
  };

  const renderEditor = () => {
    // Fix: Safely extract the component type for the switch statement
    const componentType = component ? (typeof component === 'object' && 'type' in component ? component.type : '') : '';

    switch (componentType) {
      case 'text':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                name="content"
                value={(editedComponent as any).content || ''}
                onChange={handleChange}
                className="min-h-[200px]"
                placeholder="Enter rich text content"
              />
              <p className="text-xs text-muted-foreground mt-1">
                For a full implementation, a rich text editor like React Quill would be integrated here.
              </p>
            </div>
          </div>
        );

      case 'image':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="file">Upload Image</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="file"
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="mt-1"
                  disabled={imageUpload.uploadState.isUploading}
                />
                {imageUpload.uploadState.isUploading && (
                  <Loader2 className="h-4 w-4 animate-spin" />
                )}
              </div>
              {imageUpload.uploadState.isUploading && (
                <div className="mt-2">
                  <div className="text-xs text-muted-foreground mb-1">
                    Uploading... {imageUpload.uploadState.uploadProgress}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${imageUpload.uploadState.uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Maximum file size: 10MB. Supports JPG, PNG, GIF, etc.
              </p>
              {(editedComponent as any).fileName && (
                <p className="text-xs text-green-600 mt-1">
                  ✓ Uploaded: {(editedComponent as any).fileName}
                  {(editedComponent as any).fileSize && ` (${formatFileSize((editedComponent as any).fileSize)})`}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="src">Image URL (or upload above)</Label>
              <Input
                id="src"
                name="src"
                value={(editedComponent as any).src || ''}
                onChange={handleChange}
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <div>
              <Label htmlFor="alt">Alt Text</Label>
              <Input
                id="alt"
                name="alt"
                value={(editedComponent as any).alt || ''}
                onChange={handleChange}
                placeholder="Descriptive text for the image"
              />
            </div>
            {imageUrl.url && (
              <div className="mt-2">
                <Label>Preview</Label>
                <div className="mt-1 border rounded-md overflow-hidden">
                  <img
                    src={imageUrl.url}
                    alt={(editedComponent as any).alt || "Preview"}
                    className="max-h-[200px] max-w-full object-contain mx-auto"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 'video':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="file">Upload Video</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="file"
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleFileChange}
                  className="mt-1"
                  disabled={videoUpload.uploadState.isUploading}
                />
                {videoUpload.uploadState.isUploading && (
                  <Loader2 className="h-4 w-4 animate-spin" />
                )}
              </div>
              {videoUpload.uploadState.isUploading && (
                <div className="mt-2">
                  <div className="text-xs text-muted-foreground mb-1">
                    Uploading... {videoUpload.uploadState.uploadProgress}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${videoUpload.uploadState.uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Maximum file size: 100MB. Supports MP4, WebM, etc.
              </p>
              {(editedComponent as any).fileName && (
                <p className="text-xs text-green-600 mt-1">
                  ✓ Uploaded: {(editedComponent as any).fileName}
                  {(editedComponent as any).fileSize && ` (${formatFileSize((editedComponent as any).fileSize)})`}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="src">Video URL (or upload above)</Label>
              <Input
                id="src"
                name="src"
                value={(editedComponent as any).src || ''}
                onChange={handleChange}
                placeholder="https://example.com/video.mp4"
              />
            </div>
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={(editedComponent as any).title || ''}
                onChange={handleChange}
                placeholder="Video title"
              />
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {videoUrl.url ? (
                <div className="mt-2 border rounded-md overflow-hidden">
                  <video
                    src={videoUrl.url}
                    controls
                    className="max-h-[200px] max-w-full mx-auto"
                  />
                </div>
              ) : (editedComponent as any).src ? (
                <div className="mt-2 border rounded-md overflow-hidden">
                  <video
                    src={(editedComponent as any).src}
                    controls
                    className="max-h-[200px] max-w-full mx-auto"
                  />
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Upload a video or enter a URL to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'youtube':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="url">YouTube URL</Label>
              <Input
                id="url"
                name="url"
                value={(editedComponent as any).url || ''}
                onChange={handleChange}
                placeholder="https://www.youtube.com/watch?v=..."
              />
              <p className="text-xs text-muted-foreground mt-1">
                Enter a YouTube video URL
              </p>
            </div>
            <div>
              <Label htmlFor="videoId">Video ID (extracted from URL)</Label>
              <Input
                id="videoId"
                name="videoId"
                value={(editedComponent as any).videoId || ''}
                onChange={handleChange}
                placeholder="dQw4w9WgXcQ"
              />
              <p className="text-xs text-muted-foreground mt-1">
                The video ID is automatically extracted from the URL, but you can edit it manually
              </p>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {(editedComponent as any).videoId ? (
                <div className="mt-2 border rounded-md overflow-hidden aspect-video">
                  <iframe
                    width="100%"
                    height="100%"
                    src={`https://www.youtube.com/embed/${(editedComponent as any).videoId}`}
                    title="YouTube video player"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Enter a YouTube URL to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'weblink':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="url">URL</Label>
              <Input
                id="url"
                name="url"
                value={(editedComponent as any).url || ''}
                onChange={handleChange}
                placeholder="https://example.com"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Enter the full URL including https://
              </p>
            </div>
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={(editedComponent as any).title || ''}
                onChange={handleChange}
                placeholder="Link title"
              />
            </div>
            <div>
              <Label htmlFor="thumbnail">Thumbnail URL</Label>
              <Input
                id="thumbnail"
                name="thumbnail"
                value={(editedComponent as any).thumbnail || ''}
                onChange={handleChange}
                placeholder="https://example.com/thumbnail.jpg"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Optional: URL to an image that represents this link
              </p>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {(editedComponent as any).url ? (
                <div className="flex items-center p-3 mt-2 border rounded-md">
                  {(editedComponent as any).thumbnail ? (
                    <img
                      src={(editedComponent as any).thumbnail}
                      alt="Thumbnail"
                      className="w-12 h-12 object-cover rounded mr-3"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-slate-100 rounded flex items-center justify-center mr-3">
                      <Link className="h-6 w-6 text-slate-400" />
                    </div>
                  )}
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).title || "Untitled Link"}
                    </div>
                    <div className="text-xs text-blue-500 truncate max-w-[300px]">
                      {(editedComponent as any).url}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Enter a URL to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'audio':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="file">Upload Audio</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="file"
                  ref={fileInputRef}
                  type="file"
                  accept="audio/*"
                  onChange={handleFileChange}
                  className="mt-1"
                  disabled={audioUpload.uploadState.isUploading}
                />
                {audioUpload.uploadState.isUploading && (
                  <Loader2 className="h-4 w-4 animate-spin" />
                )}
              </div>
              {audioUpload.uploadState.isUploading && (
                <div className="mt-2">
                  <div className="text-xs text-muted-foreground mb-1">
                    Uploading... {audioUpload.uploadState.uploadProgress}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${audioUpload.uploadState.uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Maximum file size: 25MB. Supports MP3, WAV, etc.
              </p>
              {(editedComponent as any).fileName && (
                <p className="text-xs text-green-600 mt-1">
                  ✓ Uploaded: {(editedComponent as any).fileName}
                  {(editedComponent as any).fileSize && ` (${formatFileSize((editedComponent as any).fileSize)})`}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="src">Audio URL (or upload above)</Label>
              <Input
                id="src"
                name="src"
                value={(editedComponent as any).src || ''}
                onChange={handleChange}
                placeholder="https://example.com/audio.mp3"
              />
            </div>
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={(editedComponent as any).title || ''}
                onChange={handleChange}
                placeholder="Audio title"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isRecorded"
                checked={(editedComponent as any).isRecorded || false}
                onCheckedChange={(checked) => handleSwitchChange('isRecorded', checked)}
              />
              <Label htmlFor="isRecorded">Recorded audio (not uploaded)</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {(editedComponent as any).src ? (
                <div className="mt-2 border rounded-md p-3">
                  <div className="flex items-center mb-2">
                    <FileAudio className="h-5 w-5 text-amber-500 mr-2" />
                    <span className="font-medium">{(editedComponent as any).title || "Audio file"}</span>
                  </div>
                  <audio
                    src={(editedComponent as any).src}
                    controls
                    className="w-full"
                  />
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Upload an audio file or enter a URL to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'embed':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="code">Embed Code</Label>
              <Textarea
                id="code"
                name="code"
                value={(editedComponent as any).code || ''}
                onChange={handleChange}
                className="min-h-[200px] font-mono text-sm"
                placeholder="<iframe src='https://example.com' width='100%' height='300'></iframe>"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Paste HTML or iframe code to embed external content
              </p>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {(editedComponent as any).code ? (
                <div className="mt-2 border rounded-md p-3">
                  <div className="mb-2 pb-2 border-b">
                    <p className="text-xs text-muted-foreground">Embed code preview:</p>
                  </div>
                  <div className="font-mono text-xs bg-slate-100 dark:bg-slate-700 p-2 rounded overflow-x-auto mb-3">
                    <code>{(editedComponent as any).code.substring(0, 100)}{(editedComponent as any).code.length > 100 ? '...' : ''}</code>
                  </div>
                  <div className="border rounded p-2 bg-white">
                    <div dangerouslySetInnerHTML={{ __html: (editedComponent as any).code }} />
                  </div>
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Enter embed code to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'scorm':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="file">Upload SCORM Package</Label>
              <Input
                id="file"
                ref={fileInputRef}
                type="file"
                accept=".zip"
                onChange={handleFileChange}
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Upload .zip files containing SCORM packages. Maximum file size: 100MB.
              </p>
            </div>
            <div>
              <Label htmlFor="fileName">File Name</Label>
              <Input
                id="fileName"
                name="fileName"
                value={(editedComponent as any).fileName || ''}
                onChange={handleChange}
                placeholder="scorm_package.zip"
              />
            </div>
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={(editedComponent as any).title || ''}
                onChange={handleChange}
                placeholder="SCORM Package Title"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Provide a descriptive title for this SCORM package
              </p>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {(editedComponent as any).fileName ? (
                <div className="mt-2 border rounded-md p-4">
                  <div className="flex items-center mb-3">
                    <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded mr-3">
                      <Boxes className="h-6 w-6 text-orange-500" />
                    </div>
                    <div>
                      <div className="font-medium">
                        {(editedComponent as any).title || (editedComponent as any).fileName}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        SCORM Package
                        {(editedComponent as any).fileSize
                          ? ` (${Math.round((editedComponent as any).fileSize / (1024 * 1024))} MB)`
                          : ""}
                      </div>
                    </div>
                  </div>
                  <div className="border rounded-md p-3 bg-slate-50 dark:bg-slate-800">
                    <p className="text-sm text-center">
                      SCORM content will be displayed in a dedicated player when launched
                    </p>
                    <div className="mt-2 flex justify-center">
                      <Button variant="outline" disabled className="text-xs">
                        Launch SCORM Content (Preview Only)
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Upload a SCORM package to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'webgl':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="file">Upload WebGL Files</Label>
              <Input
                id="file"
                ref={fileInputRef}
                type="file"
                accept=".html,.zip"
                onChange={handleFileChange}
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Upload .html or .zip files containing WebGL content. Maximum file size: 100MB.
              </p>
            </div>
            <div>
              <Label htmlFor="src">WebGL URL (or upload above)</Label>
              <Input
                id="src"
                name="src"
                value={(editedComponent as any).src || ''}
                onChange={handleChange}
                placeholder="https://example.com/webgl/index.html"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Enter a URL to WebGL content or upload files above
              </p>
            </div>
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={(editedComponent as any).title || ''}
                onChange={handleChange}
                placeholder="WebGL Content Title"
              />
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {((editedComponent as any).src || (editedComponent as any).fileName) ? (
                <div className="mt-2 border rounded-md p-4">
                  <div className="flex items-center mb-3">
                    <div className="p-2 bg-cyan-100 dark:bg-cyan-900 rounded mr-3">
                      <Gamepad2 className="h-6 w-6 text-cyan-500" />
                    </div>
                    <div>
                      <div className="font-medium">
                        {(editedComponent as any).title || (editedComponent as any).fileName || "WebGL Content"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        WebGL Interactive Content
                        {(editedComponent as any).fileSize
                          ? ` (${Math.round((editedComponent as any).fileSize / (1024 * 1024))} MB)`
                          : ""}
                      </div>
                    </div>
                  </div>
                  <div className="border rounded-md p-3 bg-slate-50 dark:bg-slate-800">
                    <div className="aspect-video bg-slate-200 dark:bg-slate-700 rounded-md flex items-center justify-center">
                      <div className="text-center p-4">
                        <Gamepad2 className="h-10 w-10 mx-auto mb-2 text-slate-400" />
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          WebGL content will be displayed in an interactive viewer when launched
                        </p>
                        <Button variant="outline" className="mt-3" disabled>
                          Launch WebGL Content (Preview Only)
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Upload WebGL files or enter a URL to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'attachment':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="file">Upload File</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="file"
                  ref={fileInputRef}
                  type="file"
                  accept="*/*"
                  onChange={handleFileChange}
                  className="mt-1"
                  disabled={fileUpload.uploadState.isUploading}
                />
                {fileUpload.uploadState.isUploading && (
                  <Loader2 className="h-4 w-4 animate-spin" />
                )}
              </div>
              {fileUpload.uploadState.isUploading && (
                <div className="mt-2">
                  <div className="text-xs text-muted-foreground mb-1">
                    Uploading... {fileUpload.uploadState.uploadProgress}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${fileUpload.uploadState.uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Maximum file size: 50MB. Supports all file types.
              </p>
              {(editedComponent as any).fileName && (
                <p className="text-xs text-green-600 mt-1">
                  ✓ Uploaded: {(editedComponent as any).fileName}
                  {(editedComponent as any).fileSize && ` (${formatFileSize((editedComponent as any).fileSize)})`}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={(editedComponent as any).title || ''}
                onChange={handleChange}
                placeholder="File title or description"
              />
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              {(editedComponent as any).fileName ? (
                <div className="mt-2 border rounded-md p-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded mr-3">
                      <FileArchive className="h-6 w-6 text-blue-500" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">
                        {(editedComponent as any).title || (editedComponent as any).fileName || "File Attachment"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        File Attachment
                        {(editedComponent as any).fileSize
                          ? ` • ${formatFileSize((editedComponent as any).fileSize)}`
                          : ""}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={attachmentUrl.isLoading || !attachmentUrl.url}
                      onClick={() => {
                        if (attachmentUrl.url) {
                          window.open(attachmentUrl.url, '_blank');
                        }
                      }}
                    >
                      {attachmentUrl.isLoading ? (
                        <>
                          <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          Loading...
                        </>
                      ) : (
                        'Download'
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800 text-center">
                  <p className="text-muted-foreground">Upload a file to see preview</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'mcq':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="question">Question</Label>
              <Textarea
                id="question"
                name="question"
                value={(editedComponent as any).question || ''}
                onChange={handleChange}
                placeholder="Enter your question here"
              />
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Options</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddOption}
                  className="h-8 px-2 text-xs"
                >
                  <PlusCircle className="h-3.5 w-3.5 mr-1" />
                  Add Option
                </Button>
              </div>
              <div className="space-y-2">
                {(editedComponent as any).options?.map((option: any, index: number) => (
                  <div key={option.id} className="flex items-center gap-2">
                    <Input
                      value={option.text}
                      onChange={(e) => handleOptionChange(option.id, e.target.value)}
                      placeholder={`Option ${index + 1}`}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveOption(option.id)}
                      className="h-8 w-8 p-0 text-destructive"
                      disabled={(editedComponent as any).options?.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="allowMultiple"
                checked={(editedComponent as any).allowMultiple || false}
                onCheckedChange={(checked) => handleSwitchChange('allowMultiple', checked)}
              />
              <Label htmlFor="allowMultiple">Allow multiple selections</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
          </div>
        );

      case 'textbox':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded mr-3">
                    <FormInput className="h-6 w-6 text-green-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Text Input Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} text input field
                    </div>
                  </div>
                </div>
                <div className="border rounded-md p-2 bg-white dark:bg-slate-700 mt-2">
                  <div className="h-8 w-full bg-slate-100 dark:bg-slate-600 rounded-md"></div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'feedback-image':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded mr-3">
                    <Upload className="h-6 w-6 text-purple-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Image Upload Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} image upload field
                    </div>
                  </div>
                </div>
                <div className="border-2 border-dashed rounded-md p-4 flex flex-col items-center justify-center mt-2 bg-white dark:bg-slate-700">
                  <Upload className="h-8 w-8 text-slate-400 mb-2" />
                  <p className="text-sm text-center text-slate-500 dark:text-slate-400">
                    Click or drag to upload an image
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'option':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Question</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter the question"
              />
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Options</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddOption}
                  className="h-8 px-2 text-xs"
                >
                  <PlusCircle className="h-3.5 w-3.5 mr-1" />
                  Add Option
                </Button>
              </div>
              <div className="space-y-2">
                {(editedComponent as any).options?.map((option: any, index: number) => (
                  <div key={option.id} className="flex items-center gap-2">
                    <Input
                      value={option.text}
                      onChange={(e) => handleOptionChange(option.id, e.target.value)}
                      placeholder={`Option ${index + 1}`}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveOption(option.id)}
                      className="h-8 w-8 p-0 text-destructive"
                      disabled={(editedComponent as any).options?.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-amber-100 dark:bg-amber-900 rounded mr-3">
                    <FileQuestion className="h-6 w-6 text-amber-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Option Question"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} option selection
                    </div>
                  </div>
                </div>
                <div className="mt-2 space-y-2">
                  {(editedComponent as any).options?.slice(0, 3).map((option: any, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-4 w-4 rounded-full border border-amber-500"></div>
                      <span className="text-sm">{option.text}</span>
                    </div>
                  ))}
                  {(editedComponent as any).options?.length > 3 && (
                    <div className="text-xs text-muted-foreground">
                      +{(editedComponent as any).options.length - 3} more options
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'checkbox':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Question</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Enter the question"
              />
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Default Options</Label>
              </div>
              <div className="space-y-2">
                {!(editedComponent as any).options || (editedComponent as any).options.length === 0 ? (
                  <div className="space-y-2">
                    {['Yes', 'No', 'Not Applicable'].map((defaultOption, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 border rounded-md">
                        <CheckSquare className="h-4 w-4 text-indigo-500" />
                        <span>{defaultOption}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {(editedComponent as any).options?.map((option: any, index: number) => (
                      <div key={option.id} className="flex items-center gap-2">
                        <Input
                          value={option.text}
                          onChange={(e) => handleOptionChange(option.id, e.target.value)}
                          placeholder={`Option ${index + 1}`}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveOption(option.id)}
                          className="h-8 w-8 p-0 text-destructive"
                          disabled={(editedComponent as any).options?.length <= 1}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded mr-3">
                    <CheckSquare className="h-6 w-6 text-indigo-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Checkbox Question"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} checkbox selection
                    </div>
                  </div>
                </div>
                <div className="mt-2 space-y-2">
                  {['Yes', 'No', 'Not Applicable'].map((defaultOption, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-4 w-4 rounded-sm border border-indigo-500"></div>
                      <span className="text-sm">{defaultOption}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'star':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded mr-3">
                    <Star className="h-6 w-6 text-yellow-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Rating Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} rating field
                    </div>
                  </div>
                </div>
                <div className="flex items-center mt-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-6 w-6 text-yellow-400 mr-1" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'date':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-red-100 dark:bg-red-900 rounded mr-3">
                    <Calendar className="h-6 w-6 text-red-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Date Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} date field
                    </div>
                  </div>
                </div>
                <div className="border rounded-md p-2 bg-white dark:bg-slate-700 mt-2 flex items-center">
                  <div className="h-8 w-full bg-slate-100 dark:bg-slate-600 rounded-md flex items-center px-3">
                    <span className="text-sm text-slate-500 dark:text-slate-400">Select a date</span>
                    <Calendar className="h-4 w-4 ml-auto text-slate-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'time':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-cyan-100 dark:bg-cyan-900 rounded mr-3">
                    <Clock className="h-6 w-6 text-cyan-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Time Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} time field
                    </div>
                  </div>
                </div>
                <div className="border rounded-md p-2 bg-white dark:bg-slate-700 mt-2 flex items-center">
                  <div className="h-8 w-full bg-slate-100 dark:bg-slate-600 rounded-md flex items-center px-3">
                    <span className="text-sm text-slate-500 dark:text-slate-400">Select a time</span>
                    <Clock className="h-4 w-4 ml-auto text-slate-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'duration':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-slate-100 dark:bg-slate-700 rounded mr-3">
                    <Timer className="h-6 w-6 text-slate-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Duration Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} duration field
                    </div>
                  </div>
                </div>
                <div className="border rounded-md p-2 bg-white dark:bg-slate-700 mt-2 flex items-center gap-2">
                  <div className="flex-1">
                    <div className="h-8 w-full bg-slate-100 dark:bg-slate-600 rounded-md flex items-center px-3">
                      <span className="text-sm text-slate-500 dark:text-slate-400">Hours</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="h-8 w-full bg-slate-100 dark:bg-slate-600 rounded-md flex items-center px-3">
                      <span className="text-sm text-slate-500 dark:text-slate-400">Minutes</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'phone':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-emerald-100 dark:bg-emerald-900 rounded mr-3">
                    <Phone className="h-6 w-6 text-emerald-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Phone Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} phone field
                    </div>
                  </div>
                </div>
                <div className="border rounded-md p-2 bg-white dark:bg-slate-700 mt-2 flex items-center">
                  <div className="h-8 w-full bg-slate-100 dark:bg-slate-600 rounded-md flex items-center px-3">
                    <span className="text-sm text-slate-500 dark:text-slate-400">Enter phone number</span>
                    <Phone className="h-4 w-4 ml-auto text-slate-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'alphanumeric':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <Switch
                id="containNumber"
                checked={(editedComponent as any).containNumber || false}
                onCheckedChange={(checked) => handleSwitchChange('containNumber', checked)}
              />
              <Label htmlFor="containNumber">Contain Number</Label>
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <Switch
                id="containLetters"
                checked={(editedComponent as any).containLetters || false}
                onCheckedChange={(checked) => handleSwitchChange('containLetters', checked)}
              />
              <Label htmlFor="containLetters">Contain Letters</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded mr-3">
                    <Hash className="h-6 w-6 text-blue-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Alphanumeric Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} alphanumeric field
                      {(editedComponent as any).containNumber && (editedComponent as any).containLetters
                        ? " (Numbers and Letters)"
                        : (editedComponent as any).containNumber
                          ? " (Numbers)"
                          : (editedComponent as any).containLetters
                            ? " (Letters)"
                            : ""}
                    </div>
                  </div>
                </div>
                <div className="border rounded-md p-2 bg-white dark:bg-slate-700 mt-2 flex items-center">
                  <div className="h-8 w-full bg-slate-100 dark:bg-slate-600 rounded-md flex items-center px-3">
                    <span className="text-sm text-slate-500 dark:text-slate-400">
                      Enter {(editedComponent as any).containNumber && (editedComponent as any).containLetters
                        ? "alphanumeric"
                        : (editedComponent as any).containNumber
                          ? "numeric"
                          : (editedComponent as any).containLetters
                            ? "alphabetic"
                            : "alphanumeric"} value
                    </span>
                    <Hash className="h-4 w-4 ml-auto text-slate-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'sign':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                name="label"
                value={(editedComponent as any).label || ''}
                onChange={handleChange}
                placeholder="Field label"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={(editedComponent as any).required}
                onCheckedChange={(checked) => handleSwitchChange('required', checked)}
              />
              <Label htmlFor="required">Required</Label>
            </div>
            <div className="mt-4">
              <Label className="text-base font-semibold">Preview</Label>
              <div className="mt-2 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded mr-3">
                    <PenTool className="h-6 w-6 text-indigo-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {(editedComponent as any).label || "Signature Field"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(editedComponent as any).required ? "Required" : "Optional"} signature field
                    </div>
                  </div>
                </div>
                <div className="border-2 border-dashed rounded-md h-24 flex items-center justify-center mt-2 bg-white dark:bg-slate-700">
                  <p className="text-muted-foreground text-sm">Signature area</p>
                </div>
              </div>
            </div>
          </div>
        );

      // Add more cases for other component types as needed

      default:
        return (
          <div className="p-4 border rounded-md bg-muted">
            <p>Editor not implemented for this component type: {componentType}</p>
          </div>
        );
    }
  };

  // Clean up object URLs when component unmounts
  React.useEffect(() => {
    return () => {
      if ((editedComponent as any).src && (editedComponent as any).src.startsWith('blob:')) {
        URL.revokeObjectURL((editedComponent as any).src);
      }
    };
  }, []);

  return (
    <div className="space-y-6 py-4">
      {renderEditor()}

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={() => onSave(editedComponent)}>
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default ComponentEditor;
