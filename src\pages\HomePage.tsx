import { useState } from "react";
import PageHeader from "@/components/common/PageHeader";
import MetricCard from "@/components/Charts/MetricCard";
import TabsContainer from "@/components/common/TabsContainer";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MultiSelect, OptionType } from "@/components/ui/multi-select";
import {
  Shield, AlertTriangle, Clock, CheckCircle, Target, TrendingUp, Award, BarChart3,
  Leaf, TreePine, Zap, Users, Settings, Cog, FileCheck, BookOpen,
  Heart, Truck, AlertOctagon, Cpu, Lightbulb,
  // Safety Performance Icons
  ShieldCheck, ShieldAlert, ClipboardCheck, Eye,
  // Quality Performance Icons
  CheckSquare, ClipboardList, XCircle, SearchCheck,
  // Environmental Performance Icons
  CloudSnow, RotateCcw, AlertOctagon as EnvAlert,
  // Risk & Compliance Icons
  ShieldX, Timer, ShieldCheck as RiskShield,
  // Operational Discipline Icons
  CheckCircle2, RotateCw, FileX, PowerOff,
  // Organizational Competence Icons
  GraduationCap, Brain, Activity,
  // Leadership Icons
  Users2, Crown, UserCheck, TrendingUp as LeadershipTrend
} from "lucide-react";


const HomePage = () => {
  // Time period selection state for each metric
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});

  // Function to get icon and color based on metric title and tab
  const getMetricIconAndColor = (title: string, tabType: string) => {
    const lowerTitle = title.toLowerCase();

    switch (tabType) {
      case 'safety':
        if (lowerTitle.includes('high-severity') || lowerTitle.includes('incident')) return { icon: ShieldAlert, color: 'bg-red-500' };
        if (lowerTitle.includes('control implementation')) return { icon: ShieldCheck, color: 'bg-red-500' };
        if (lowerTitle.includes('verification')) return { icon: ClipboardCheck, color: 'bg-red-500' };
        if (lowerTitle.includes('behavior')) return { icon: Eye, color: 'bg-red-500' };
        return { icon: Shield, color: 'bg-red-500' };

      case 'quality':
        if (lowerTitle.includes('first pass') || lowerTitle.includes('fpv')) return { icon: CheckSquare, color: 'bg-blue-500' };
        if (lowerTitle.includes('inspection')) return { icon: ClipboardList, color: 'bg-blue-500' };
        if (lowerTitle.includes('non-conformance')) return { icon: XCircle, color: 'bg-blue-500' };
        if (lowerTitle.includes('verification')) return { icon: SearchCheck, color: 'bg-blue-500' };
        return { icon: BarChart3, color: 'bg-blue-500' };

      case 'environmental':
        if (lowerTitle.includes('carbon') || lowerTitle.includes('emissions')) return { icon: CloudSnow, color: 'bg-green-500' };
        if (lowerTitle.includes('renewable') || lowerTitle.includes('energy')) return { icon: Zap, color: 'bg-green-500' };
        if (lowerTitle.includes('recycling') || lowerTitle.includes('waste')) return { icon: RotateCcw, color: 'bg-green-500' };
        if (lowerTitle.includes('compliance') || lowerTitle.includes('regulatory')) return { icon: EnvAlert, color: 'bg-green-500' };
        return { icon: Leaf, color: 'bg-green-500' };

      case 'risk':
        if (lowerTitle.includes('unimplemented') || lowerTitle.includes('safety controls')) return { icon: ShieldX, color: 'bg-orange-500' };
        if (lowerTitle.includes('inadequate') || lowerTitle.includes('execution')) return { icon: AlertTriangle, color: 'bg-orange-500' };
        if (lowerTitle.includes('timely') || lowerTitle.includes('compliance action')) return { icon: Timer, color: 'bg-orange-500' };
        if (lowerTitle.includes('preventive') || lowerTitle.includes('focus')) return { icon: RiskShield, color: 'bg-orange-500' };
        return { icon: Shield, color: 'bg-orange-500' };

      case 'operational':
        if (lowerTitle.includes('on-time') || lowerTitle.includes('compliance action')) return { icon: CheckCircle2, color: 'bg-purple-500' };
        if (lowerTitle.includes('first-time') || lowerTitle.includes('closure')) return { icon: CheckCircle, color: 'bg-purple-500' };
        if (lowerTitle.includes('deviation') || lowerTitle.includes('procedure')) return { icon: FileX, color: 'bg-purple-500' };
        if (lowerTitle.includes('downtime') || lowerTitle.includes('unplanned')) return { icon: PowerOff, color: 'bg-purple-500' };
        return { icon: Cog, color: 'bg-purple-500' };

      case 'organizational':
        if (lowerTitle.includes('knowledge coverage') || lowerTitle.includes('training')) return { icon: GraduationCap, color: 'bg-indigo-500' };
        if (lowerTitle.includes('critical competence') || lowerTitle.includes('algorithm')) return { icon: Brain, color: 'bg-indigo-500' };
        if (lowerTitle.includes('learning engagement') || lowerTitle.includes('participation')) return { icon: Activity, color: 'bg-indigo-500' };
        if (lowerTitle.includes('retention') || lowerTitle.includes('knowledge')) return { icon: BookOpen, color: 'bg-indigo-500' };
        return { icon: Users, color: 'bg-indigo-500' };

      case 'leadership':
        if (lowerTitle.includes('walkthrough') || lowerTitle.includes('leader')) return { icon: Users2, color: 'bg-yellow-500' };
        if (lowerTitle.includes('action ownership') || lowerTitle.includes('accountability')) return { icon: Crown, color: 'bg-yellow-500' };
        if (lowerTitle.includes('good catch') || lowerTitle.includes('observations')) return { icon: Eye, color: 'bg-yellow-500' };
        if (lowerTitle.includes('innovation') || lowerTitle.includes('agility')) return { icon: Lightbulb, color: 'bg-yellow-500' };
        return { icon: Users, color: 'bg-yellow-500' };

      default:
        return { icon: BarChart3, color: 'bg-gray-500' };
    }
  };

  // Hierarchical filter states
  const [selectedCountry, setSelectedCountry] = useState<string>('');
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedSites, setSelectedSites] = useState<string[]>([]);

  // Applied filter states (what's actually being used for filtering)
  const [appliedCountry, setAppliedCountry] = useState<string>('');
  const [appliedRegions, setAppliedRegions] = useState<string[]>([]);
  const [appliedSites, setAppliedSites] = useState<string[]>([]);

  // Track if filters have been applied (for button visibility)
  const [filtersApplied, setFiltersApplied] = useState<boolean>(false);

  // Mock data for dropdowns
  const countries = ['United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Australia', 'Japan'];

  // Mock hierarchical data - in real app, this would come from API based on selected country
  const getRegionsForCountry = (country: string): OptionType[] => {
    const regionMap: Record<string, OptionType[]> = {
      'United States': [
        { label: 'Northeast', value: 'northeast' },
        { label: 'Southeast', value: 'southeast' },
        { label: 'Midwest', value: 'midwest' },
        { label: 'Southwest', value: 'southwest' },
        { label: 'West', value: 'west' }
      ],
      'Canada': [
        { label: 'Eastern Canada', value: 'eastern-canada' },
        { label: 'Western Canada', value: 'western-canada' },
        { label: 'Central Canada', value: 'central-canada' }
      ],
      'United Kingdom': [
        { label: 'England', value: 'england' },
        { label: 'Scotland', value: 'scotland' },
        { label: 'Wales', value: 'wales' },
        { label: 'Northern Ireland', value: 'northern-ireland' }
      ],
      'Germany': [
        { label: 'North Rhine-Westphalia', value: 'nrw' },
        { label: 'Bavaria', value: 'bavaria' },
        { label: 'Baden-Württemberg', value: 'baden-wurttemberg' }
      ],
      'France': [
        { label: 'Île-de-France', value: 'ile-de-france' },
        { label: 'Provence-Alpes-Côte d\'Azur', value: 'paca' },
        { label: 'Auvergne-Rhône-Alpes', value: 'auvergne-rhone-alpes' }
      ],
      'Australia': [
        { label: 'New South Wales', value: 'nsw' },
        { label: 'Victoria', value: 'victoria' },
        { label: 'Queensland', value: 'queensland' },
        { label: 'Western Australia', value: 'wa' }
      ],
      'Japan': [
        { label: 'Kantō', value: 'kanto' },
        { label: 'Kansai', value: 'kansai' },
        { label: 'Chūbu', value: 'chubu' }
      ]
    };
    return regionMap[country] || [];
  };

  // Mock sites data - in real app, this would come from API based on selected regions
  const getSitesForRegions = (regions: string[]): OptionType[] => {
    const siteMap: Record<string, OptionType[]> = {
      'northeast': [
        { label: 'Boston Manufacturing Plant', value: 'boston-mfg' },
        { label: 'New York Distribution Center', value: 'ny-dist' }
      ],
      'southeast': [
        { label: 'Atlanta Operations Hub', value: 'atlanta-ops' },
        { label: 'Miami Research Facility', value: 'miami-research' }
      ],
      'midwest': [
        { label: 'Chicago Processing Center', value: 'chicago-proc' },
        { label: 'Detroit Assembly Plant', value: 'detroit-assembly' }
      ],
      'southwest': [
        { label: 'Dallas Logistics Center', value: 'dallas-logistics' },
        { label: 'Houston Refinery', value: 'houston-refinery' }
      ],
      'west': [
        { label: 'Los Angeles Port Facility', value: 'la-port' },
        { label: 'San Francisco Tech Center', value: 'sf-tech' }
      ],
      'eastern-canada': [
        { label: 'Toronto Manufacturing', value: 'toronto-mfg' },
        { label: 'Montreal Distribution', value: 'montreal-dist' }
      ],
      'western-canada': [
        { label: 'Vancouver Operations', value: 'vancouver-ops' },
        { label: 'Calgary Energy Hub', value: 'calgary-energy' }
      ],
      'central-canada': [
        { label: 'Winnipeg Processing', value: 'winnipeg-proc' }
      ],
      'england': [
        { label: 'London Headquarters', value: 'london-hq' },
        { label: 'Manchester Plant', value: 'manchester-plant' }
      ],
      'scotland': [
        { label: 'Edinburgh Research Center', value: 'edinburgh-research' }
      ],
      'wales': [
        { label: 'Cardiff Operations', value: 'cardiff-ops' }
      ],
      'northern-ireland': [
        { label: 'Belfast Manufacturing', value: 'belfast-mfg' }
      ]
    };

    const allSites: OptionType[] = [];
    regions.forEach(region => {
      const sites = siteMap[region] || [];
      allSites.push(...sites);
    });
    return allSites;
  };

  // Handle country selection - resets dependent dropdowns
  const handleCountryChange = (country: string) => {
    console.log('Country changed to:', country);
    setSelectedCountry(country);
    setSelectedRegions([]); // Reset regions when country changes
    setSelectedSites([]); // Reset sites when country changes
  };

  // Handle region selection - resets dependent dropdowns
  const handleRegionChange = (regions: string[]) => {
    console.log('Regions changed to:', regions);
    setSelectedRegions(regions);
    setSelectedSites([]); // Reset sites when regions change
  };

  // Handle site selection
  const handleSiteChange = (sites: string[]) => {
    console.log('Sites changed to:', sites);
    setSelectedSites(sites);
  };

  // Wrapper functions to avoid TypeScript conflicts with HTMLAttributes
  const onRegionSelectionChange = (selected: string[]) => {
    handleRegionChange(selected);
  };

  const onSiteSelectionChange = (selected: string[]) => {
    handleSiteChange(selected);
  };

  // Check if Apply button should be visible
  const shouldShowApplyButton = () => {
    return selectedCountry !== '' &&
           selectedRegions.length > 0 &&
           selectedSites.length > 0;
  };

  // Check if Clear button should be visible
  const shouldShowClearButton = () => {
    return filtersApplied;
  };

  // Handle Apply button click
  const handleApplyFilters = () => {
    setAppliedCountry(selectedCountry);
    setAppliedRegions([...selectedRegions]);
    setAppliedSites([...selectedSites]);
    setFiltersApplied(true);
    console.log('Filters applied:', {
      country: selectedCountry,
      regions: selectedRegions,
      sites: selectedSites
    });
  };

  // Handle Clear button click - Reset all filters
  const handleClearFilters = () => {
    setSelectedCountry('');
    setSelectedRegions([]);
    setSelectedSites([]);
    setAppliedCountry('');
    setAppliedRegions([]);
    setAppliedSites([]);
    setFiltersApplied(false);
    console.log('Filters cleared - reset to empty');
  };

  // Handle time period changes for metrics
  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  // Get selected period for a specific metric
  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  // Performance Metrics Data - Structured for easy API integration
  const getPerformanceMetrics = () => {
    // Safety Performance Metrics
    const safetyMetrics = [
      {
        id: "high-severity-incident-count",
        title: "High-Severity Incident Count",
        description: "Absolute number of high-severity or reportable safety incidents—such as those resulting in significant harm, hospitalization, regulatory notification, or fatalities—recorded over a 12-month rolling period. Reflects the scale of impactful safety failures and supports strategic focus on high-consequence risk reduction.",
        value: "1",
        unit: "incidents",
        targetPercentage: -66.7,
        trend: [5, 4, 4, 3, 3, 2, 2, 1, 1, 1, 1, 1],
        isImproving: true
      },
      {
        id: "control-implementation-rate",
        title: "Control Implementation Rate",
        description: "Number of safety controls identified in risk assessments that have been fully implemented within the intended timeframe. Tracked as a 12‑month rolling count to assess control deployment discipline.",
        value: "247",
        unit: "controls",
        targetPercentage: 18.3,
        trend: [195, 205, 215, 220, 225, 230, 235, 240, 243, 245, 246, 247],
        isImproving: true
      },
      {
        id: "control-verification-completion",
        title: "Control Verification Completion",
        description: "Total number of implemented controls that have been verified or validated (via audits, inspections, or toolbox talks) in the field. A leading signal that controls are not just present but functioning effectively.",
        value: "189",
        unit: "verifications",
        targetPercentage: 24.7,
        trend: [142, 150, 158, 165, 170, 175, 180, 183, 186, 187, 188, 189],
        isImproving: true
      },
      {
        id: "percent-safe-behavior-rate",
        title: "Percent Safe Behavior Rate",
        description: "Percentage of behaviors observed that are classified as 'safe' vs. 'unsafe' during behavioral observations or audits. Reflects control adherence and behavioral discipline. Higher rates = stronger implementation culture.",
        value: "92.4",
        unit: "%",
        targetPercentage: 12.8,
        trend: [78, 81, 84, 86, 88, 89, 90, 91, 91.5, 92, 92.2, 92.4],
        isImproving: true
      }
    ];

    // Quality Performance Metrics
    const qualityMetrics = [
      {
        id: "first-pass-yield-fpv",
        title: "First Pass Yield (FPY)",
        description: "Percentage of units passing quality standards on the first attempt—without rework, repair, or adjustment—over a 12-month rolling period. A higher FPY indicates efficient, error-free production and helps pinpoint where processes may be failing or need improvement.",
        value: "94.2",
        unit: "%",
        targetPercentage: 8.7,
        trend: [85, 87, 89, 90, 91, 92, 93, 93.5, 94, 94.1, 94.15, 94.2],
        isImproving: true
      },
      {
        id: "inspection-compliance-rate",
        title: "Inspection Compliance Rate",
        description: "Percentage of mandatory checklist-based quality inspections completed as scheduled over a 12‑month rolling period. Signals whether quality routines are being systematically followed and highlights potential gaps in oversight.",
        value: "91.8",
        unit: "%",
        targetPercentage: 14.2,
        trend: [78, 80, 82, 84, 86, 88, 89, 90, 91, 91.3, 91.6, 91.8],
        isImproving: true
      },
      {
        id: "non-conformance-findings",
        title: "Non‑Conformance Findings",
        description: "Total number of non‑conformities flagged during inspections or audits over a 12‑month rolling count. Helps leadership pinpoint persistent process weaknesses and guide corrective actions.",
        value: "23",
        unit: "findings",
        targetPercentage: -35.2,
        trend: [42, 38, 35, 32, 30, 28, 26, 25, 24, 23, 23, 23],
        isImproving: true
      },
      {
        id: "control-verification-rate-quality",
        title: "Control Verification Rate",
        description: "Number of implemented quality controls verified through follow-up inspections or observations over a 12‑month rolling count. Indicates whether expected measures are not only present but effectively enforced in practice.",
        value: "156",
        unit: "verifications",
        targetPercentage: 28.9,
        trend: [108, 115, 122, 128, 135, 140, 145, 150, 152, 154, 155, 156],
        isImproving: true
      }
    ];

    // Environmental Performance Metrics
    const environmentalMetrics = [
      {
        id: "carbon-emissions-scope-1-2",
        title: "Carbon Emissions (Scope 1 & 2)",
        description: "Total Scope 1 and Scope 2 greenhouse gas emissions (in CO₂e) generated by company operations over a 12‑month rolling period. Crucial for tracking direct environmental footprint and decarbonization efforts.",
        value: "2,456",
        unit: "tCO₂e",
        targetPercentage: -18.2,
        trend: [3200, 3050, 2900, 2800, 2700, 2650, 2600, 2550, 2500, 2480, 2470, 2456],
        isImproving: true
      },
      {
        id: "renewable-energy-ratio",
        title: "Renewable Energy Ratio",
        description: "Percentage of total energy consumption sourced from renewables (solar, wind, hydro, biomass), tracked as a 12‑month rolling rate. Reflects progress toward decarbonization and green energy targets.",
        value: "42.8",
        unit: "%",
        targetPercentage: 35.4,
        trend: [25, 28, 31, 33, 35, 37, 38, 39, 40, 41, 42, 42.8],
        isImproving: true
      },
      {
        id: "waste-recycling-rate",
        title: "Waste Recycling Rate",
        description: "Percentage of total waste (kg or tonnes) diverted to recycling or reuse versus total waste generated, tracked as a 12‑month rolling rate. Strong indicator of circular economy practices and waste minimization.",
        value: "76.4",
        unit: "%",
        targetPercentage: 22.6,
        trend: [58, 61, 64, 66, 68, 70, 72, 73, 74, 75, 76, 76.4],
        isImproving: true
      },
      {
        id: "regulatory-non-compliance-count",
        title: "Regulatory Non‑Compliance Count",
        description: "Absolute number of environmental compliance breaches—such as permit violations or regulatory exceedances—recorded over a 12‑month rolling count. Signals areas where compliance systems or controls may need strengthening.",
        value: "2",
        unit: "breaches",
        targetPercentage: -71.4,
        trend: [9, 8, 7, 6, 5, 4, 3, 3, 2, 2, 2, 2],
        isImproving: true
      }
    ];

    // Risk Management Effectiveness Metrics
    const riskManagementMetrics = [
      {
        id: "unimplemented-safety-controls",
        title: "Unimplemented Safety Controls",
        description: "Total number of safety controls identified through risk assessments that remain unimplemented or are overdue. Tracked as a 12-month rolling count to highlight persistent gaps in risk mitigation efforts.",
        value: "8",
        unit: "controls",
        targetPercentage: -42.9,
        trend: [18, 16, 15, 14, 12, 11, 10, 9, 8, 8, 8, 8],
        isImproving: true
      },
      {
        id: "inadequate-controls-execution",
        title: "Inadequate Controls During Execution",
        description: "Number of risk controls flagged as inadequate during toolbox talks linked to prior risk assessments. Tracked as a 12-month rolling count, this reflects gaps between planned controls and actual work conditions, indicating opportunities to improve risk assessment quality.",
        value: "5",
        unit: "controls",
        targetPercentage: -58.3,
        trend: [15, 13, 12, 10, 9, 8, 7, 6, 5, 5, 5, 5],
        isImproving: true
      },
      {
        id: "timely-compliance-action-rate",
        title: "Timely Compliance Action Rate",
        description: "Percentage of compliance-related actions (e.g., regulatory, internal standards) closed on or before the due date. Calculated as a 12-month rolling rate, this metric indicates adherence to compliance timelines and governance discipline.",
        value: "91.4",
        unit: "%",
        targetPercentage: 14.2,
        trend: [78, 81, 83, 85, 87, 88, 89, 90, 91, 91.2, 91.3, 91.4],
        isImproving: true
      },
      {
        id: "preventive-control-focus-ratio",
        title: "Preventive Control Focus Ratio",
        description: "Ratio of preventive controls to total controls identified in risk assessments over a 12-month rolling period. A higher ratio reflects a proactive risk management approach focused on eliminating hazards before they occur, rather than simply mitigating consequences.",
        value: "68.7",
        unit: "%",
        targetPercentage: 22.5,
        trend: [52, 55, 58, 60, 62, 64, 65, 66, 67, 68, 68.5, 68.7],
        isImproving: true
      }
    ];

    // Operational Discipline Metrics
    const operationalDisciplineMetrics = [
      {
        id: "on-time-compliance-action-rate",
        title: "On-Time Compliance Action Rate",
        description: "Percentage of compliance or corrective actions (assigned from audits, inspections, or observations) closed on or before their assigned due dates — calculated as a 12‑month rolling rate. Reflects disciplined execution aligned to planned timelines and realistic target setting.",
        value: "89.3",
        unit: "%",
        targetPercentage: 16.4,
        trend: [72, 75, 78, 80, 82, 84, 86, 87, 88, 88.5, 89, 89.3],
        isImproving: true
      },
      {
        id: "first-time-closure-rate",
        title: "First-Time Closure Rate",
        description: "Percentage of compliance or corrective actions closed successfully on the first attempt—i.e., with no rework, reopenings, or follow-up required. Measured over a 12‑month rolling period, this reflects the effectiveness and quality of corrective action execution.",
        value: "76.8",
        unit: "%",
        targetPercentage: 22.1,
        trend: [58, 62, 65, 68, 70, 72, 73, 74, 75, 76, 76.5, 76.8],
        isImproving: true
      },
      {
        id: "procedure-deviation-count",
        title: "Procedure Deviation Count",
        description: "Number of deviations from standard operating procedures (SOPs) recorded during audits, inspections, or safety observations over a 12-month rolling period. A high count may indicate poor operational discipline, insufficient training, or impractical procedures.",
        value: "12",
        unit: "deviations",
        targetPercentage: -45.5,
        trend: [28, 25, 22, 20, 18, 16, 15, 14, 13, 12, 12, 12],
        isImproving: true
      },
      {
        id: "unplanned-downtime-hours",
        title: "Unplanned Downtime Hours",
        description: "Total hours of unplanned downtime (from equipment or process interruptions) recorded over a 12-month rolling period. A key performance indicator for operational resilience and preventive maintenance effectiveness.",
        value: "156",
        unit: "hours",
        targetPercentage: -38.2,
        trend: [285, 260, 240, 220, 200, 185, 175, 165, 160, 158, 157, 156],
        isImproving: true
      }
    ];

    // Process Reliability & Efficiency Metrics
    const processReliabilityMetrics = [
      {
        id: "equipment-uptime",
        title: "Equipment Uptime",
        value: "97.2",
        unit: "%",
        targetPercentage: 3.8,
        trend: [92, 93, 94, 95, 95.5, 96, 96.5, 97, 97.1, 97.15, 97.18, 97.2],
        isImproving: true
      },
      {
        id: "process-efficiency",
        title: "Process Efficiency",
        value: "89.6",
        unit: "%",
        targetPercentage: 11.2,
        trend: [78, 80, 82, 84, 85, 86, 87, 88, 89, 89.3, 89.5, 89.6],
        isImproving: true
      },
      {
        id: "throughput-rate",
        title: "Throughput Rate",
        value: "1,247",
        unit: "units/hr",
        targetPercentage: 8.9,
        trend: [1100, 1120, 1140, 1160, 1180, 1200, 1220, 1230, 1240, 1243, 1245, 1247],
        isImproving: true
      },
      {
        id: "cycle-time-variance",
        title: "Cycle Time Variance",
        value: "4.2",
        unit: "%",
        targetPercentage: -28.8,
        trend: [7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.8, 4.6, 4.4, 4.3, 4.25, 4.2],
        isImproving: true
      }
    ];

    // Compliance & Regulatory Adherence Metrics
    const complianceMetrics = [
      {
        id: "regulatory-compliance",
        title: "Regulatory Compliance",
        value: "98.7",
        unit: "%",
        targetPercentage: 2.1,
        trend: [95, 96, 96.5, 97, 97.5, 98, 98.2, 98.4, 98.5, 98.6, 98.65, 98.7],
        isImproving: true
      },
      {
        id: "permit-renewals",
        title: "Permit Renewals",
        value: "100",
        unit: "%",
        targetPercentage: 0.0,
        trend: [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100],
        isImproving: true
      },
      {
        id: "audit-findings",
        title: "Audit Findings",
        value: "2",
        unit: "findings",
        targetPercentage: -60.0,
        trend: [8, 7, 6, 5, 4, 3, 3, 2, 2, 2, 2, 2],
        isImproving: true
      },
      {
        id: "compliance-training",
        title: "Compliance Training",
        value: "95.3",
        unit: "%",
        targetPercentage: 6.7,
        trend: [87, 89, 90, 91, 92, 93, 94, 94.5, 95, 95.1, 95.2, 95.3],
        isImproving: true
      }
    ];

    // Organizational Competence & Learning Metrics
    const organizationalCompetenceMetrics = [
      {
        id: "knowledge-coverage-ratio",
        title: "Knowledge Coverage Ratio",
        description: "Percentage of employees who have completed training mapped to their designated job roles and responsibilities. Indicates organizational alignment between required knowledge and actual learning coverage over a 12-month rolling period.",
        value: "87.2",
        unit: "%",
        targetPercentage: 12.8,
        trend: [75, 78, 80, 82, 84, 85, 86, 86.5, 87, 87.1, 87.1, 87.2],
        isImproving: true
      },
      {
        id: "critical-competence-index",
        title: "Critical Competence Index",
        description: "An algorithm-based measure of how accurately and confidently individuals apply critical knowledge in high-risk training areas. Reflects depth of understanding in areas where errors can have serious consequences — calculated as a 12-month rolling index.",
        value: "82.4",
        unit: "index",
        targetPercentage: 15.6,
        trend: [68, 71, 74, 76, 78, 79, 80, 81, 81.5, 82, 82.2, 82.4],
        isImproving: true
      },
      {
        id: "learning-engagement-rate",
        title: "Learning Engagement Rate",
        description: "Percentage of employees actively participating in online learning reinforcement activities by responding to push notifications and completing quizzes. Tracked as a 12-month rolling average to assess sustained commitment to continuous learning.",
        value: "74.8",
        unit: "%",
        targetPercentage: 18.3,
        trend: [58, 62, 65, 68, 70, 71, 72, 73, 74, 74.3, 74.6, 74.8],
        isImproving: true
      },
      {
        id: "knowledge-retention-index",
        title: "Knowledge Retention Index",
        description: "Reflects how well individuals retain knowledge over time through periodic online spaced reinforcement quizzes. This 12-month rolling index is generated using an algorithm that evaluates the accuracy of responses across various topics to provide a consistent and objective measure of retention.",
        value: "79.6",
        unit: "index",
        targetPercentage: 21.2,
        trend: [62, 65, 68, 71, 73, 75, 76, 77, 78, 79, 79.3, 79.6],
        isImproving: true
      }
    ];

    // Leadership Engagement & Culture Metrics
    const leadershipEngagementMetrics = [
      {
        id: "leader-walkthroughs",
        title: "Leader Walkthroughs",
        description: "Number of on-site walkthroughs conducted by leaders, recorded through positive or negative reported observations across Safety, Quality, Environment, or other operational topics.",
        value: "24",
        unit: "walkthroughs",
        targetPercentage: 20.0,
        trend: [18, 19, 20, 21, 22, 22, 23, 23, 24, 24, 24, 24],
        isImproving: true
      },
      {
        id: "leader-action-ownership-rate",
        title: "Leader Action Ownership Rate",
        description: "Percentage of total action items (arising from audits, incidents, observations, campaigns, etc.) that are either directly owned (assigned to) or closed by leaders. ",
        value: "78.5",
        unit: "%",
        targetPercentage: 15.2,
        trend: [65, 68, 70, 72, 74, 75, 76, 77, 78, 78.2, 78.3, 78.5],
        isImproving: true
      },
      {
        id: "good-catch-rate",
        title: "Good Catch Rate",
        description: "Number of proactive observations or suggestions submitted by employees that help prevent potential safety, environment and health issues in operational areas.",
        value: "142",
        unit: "observations",
        targetPercentage: 28.6,
        trend: [95, 105, 115, 120, 125, 130, 135, 138, 140, 141, 141, 142],
        isImproving: true
      },
      {
        id: "innovation-agility-index",
        title: "Innovation and Agility Index",
        description: "Tracks the number of employee-initiated improvement ideas, process innovations, or change suggestions submitted and actioned.",
        value: "89",
        unit: "ideas",
        targetPercentage: 34.8,
        trend: [55, 60, 65, 70, 75, 78, 82, 85, 87, 88, 88, 89],
        isImproving: true
      }
    ];

    // Employee Engagement & Wellbeing Metrics
    const employeeEngagementMetrics = [
      {
        id: "engagement-score",
        title: "Engagement Score",
        value: "76.8",
        unit: "%",
        targetPercentage: 15.3,
        trend: [64, 66, 68, 70, 72, 73, 74, 75, 76, 76.3, 76.6, 76.8],
        isImproving: true
      },
      {
        id: "wellbeing-index",
        title: "Wellbeing Index",
        value: "81.2",
        unit: "index",
        targetPercentage: 12.7,
        trend: [70, 72, 74, 76, 77, 78, 79, 80, 80.5, 80.8, 81, 81.2],
        isImproving: true
      },
      {
        id: "retention-rate",
        title: "Retention Rate",
        value: "94.6",
        unit: "%",
        targetPercentage: 5.8,
        trend: [88, 89, 90, 91, 92, 93, 93.5, 94, 94.2, 94.3, 94.5, 94.6],
        isImproving: true
      },
      {
        id: "work-life-balance",
        title: "Work-Life Balance",
        value: "4.1",
        unit: "/5.0",
        targetPercentage: 17.1,
        trend: [3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0, 4.05, 4.08, 4.09, 4.1],
        isImproving: true
      }
    ];

    // Supply Chain & Contractor Performance Metrics
    const supplyChainMetrics = [
      {
        id: "supplier-performance",
        title: "Supplier Performance",
        value: "88.4",
        unit: "%",
        targetPercentage: 10.6,
        trend: [78, 80, 82, 84, 85, 86, 87, 87.5, 88, 88.2, 88.3, 88.4],
        isImproving: true
      },
      {
        id: "contractor-safety",
        title: "Contractor Safety",
        value: "96.2",
        unit: "%",
        targetPercentage: 4.3,
        trend: [91, 92, 93, 94, 95, 95.5, 96, 96.1, 96.15, 96.18, 96.19, 96.2],
        isImproving: true
      },
      {
        id: "delivery-performance",
        title: "Delivery Performance",
        value: "92.7",
        unit: "%",
        targetPercentage: 7.8,
        trend: [84, 86, 87, 88, 89, 90, 91, 91.5, 92, 92.3, 92.5, 92.7],
        isImproving: true
      },
      {
        id: "cost-efficiency",
        title: "Cost Efficiency",
        value: "15.3",
        unit: "% savings",
        targetPercentage: 22.4,
        trend: [8, 9, 10, 11, 12, 13, 14, 14.5, 15, 15.1, 15.2, 15.3],
        isImproving: true
      }
    ];

    // Crisis Preparedness & Business Continuity Metrics
    const crisisPreparednessMetrics = [
      {
        id: "emergency-response-time",
        title: "Emergency Response Time",
        value: "3.2",
        unit: "minutes",
        targetPercentage: -36.0,
        trend: [6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.8, 3.6, 3.4, 3.3, 3.25, 3.2],
        isImproving: true
      },
      {
        id: "drill-effectiveness",
        title: "Drill Effectiveness",
        value: "94.8",
        unit: "%",
        targetPercentage: 8.6,
        trend: [85, 87, 88, 89, 90, 91, 92, 93, 94, 94.3, 94.6, 94.8],
        isImproving: true
      },
      {
        id: "business-continuity",
        title: "Business Continuity",
        value: "97.1",
        unit: "%",
        targetPercentage: 2.8,
        trend: [93, 94, 95, 95.5, 96, 96.3, 96.6, 96.8, 97, 97.05, 97.08, 97.1],
        isImproving: true
      },
      {
        id: "recovery-time",
        title: "Recovery Time",
        value: "2.4",
        unit: "hours",
        targetPercentage: -52.0,
        trend: [6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.8, 2.6, 2.5, 2.45, 2.4],
        isImproving: true
      }
    ];

    // Digital Maturity & Automation Metrics
    const digitalMaturityMetrics = [
      {
        id: "automation-level",
        title: "Automation Level",
        value: "67.3",
        unit: "%",
        targetPercentage: 24.8,
        trend: [45, 48, 51, 54, 57, 60, 62, 64, 65, 66, 66.5, 67.3],
        isImproving: true
      },
      {
        id: "digital-adoption",
        title: "Digital Adoption",
        value: "82.6",
        unit: "%",
        targetPercentage: 18.4,
        trend: [65, 68, 70, 72, 74, 76, 78, 79, 80, 81, 82, 82.6],
        isImproving: true
      },
      {
        id: "system-integration",
        title: "System Integration",
        value: "74.9",
        unit: "%",
        targetPercentage: 31.6,
        trend: [50, 53, 56, 59, 62, 65, 68, 70, 72, 73, 74, 74.9],
        isImproving: true
      },
      {
        id: "data-quality",
        title: "Data Quality",
        value: "91.4",
        unit: "%",
        targetPercentage: 14.2,
        trend: [78, 80, 82, 84, 86, 87, 88, 89, 90, 90.5, 91, 91.4],
        isImproving: true
      }
    ];

    // Continuous Improvement / Innovation Metrics
    const continuousImprovementMetrics = [
      {
        id: "improvement-ideas",
        title: "Improvement Ideas",
        value: "127",
        unit: "ideas",
        targetPercentage: 35.1,
        trend: [80, 85, 90, 95, 100, 105, 110, 115, 120, 123, 125, 127],
        isImproving: true
      },
      {
        id: "implementation-rate",
        title: "Implementation Rate",
        value: "73.8",
        unit: "%",
        targetPercentage: 23.5,
        trend: [55, 58, 61, 64, 66, 68, 70, 71, 72, 73, 73.5, 73.8],
        isImproving: true
      },
      {
        id: "innovation-index",
        title: "Innovation Index",
        value: "68.2",
        unit: "index",
        targetPercentage: 28.3,
        trend: [45, 48, 51, 54, 57, 60, 62, 64, 66, 67, 67.5, 68.2],
        isImproving: true
      },
      {
        id: "cost-savings",
        title: "Cost Savings",
        value: "2.4M",
        unit: "USD",
        targetPercentage: 44.0,
        trend: [1.2, 1.4, 1.6, 1.8, 2.0, 2.1, 2.2, 2.25, 2.3, 2.35, 2.38, 2.4],
        isImproving: true
      }
    ];

    return {
      safetyMetrics,
      qualityMetrics,
      environmentalMetrics,
      riskManagementMetrics,
      operationalDisciplineMetrics,
      processReliabilityMetrics,
      complianceMetrics,
      organizationalCompetenceMetrics,
      leadershipEngagementMetrics,
      employeeEngagementMetrics,
      supplyChainMetrics,
      crisisPreparednessMetrics,
      digitalMaturityMetrics,
      continuousImprovementMetrics
    };
  };

  const {
    safetyMetrics,
    qualityMetrics,
    environmentalMetrics,
    riskManagementMetrics,
    operationalDisciplineMetrics,
    processReliabilityMetrics,
    complianceMetrics,
    organizationalCompetenceMetrics,
    leadershipEngagementMetrics,
    employeeEngagementMetrics,
    supplyChainMetrics,
    crisisPreparednessMetrics,
    digitalMaturityMetrics,
    continuousImprovementMetrics
  } = getPerformanceMetrics();

  // Performance sections configuration for easy maintenance and API integration
  const performanceSections = [
    {
      id: 'safety',
      title: 'Safety Performance',
      description: 'Key performance indicators to track overall safety performance of the operating entity.',
      metrics: safetyMetrics
    },
    {
      id: 'quality',
      title: 'Quality Performance',
      description: 'Indicators reflecting product and process quality across operations.',
      metrics: qualityMetrics
    },
    {
      id: 'environmental',
      title: 'Environmental Performance',
      description: 'Performance insights on environmental impact and sustainability efforts.',
      metrics: environmentalMetrics
    },
    {
      id: 'risk-management',
      title: 'Risk Management Effectiveness',
      description: 'Overview of how effectively risks are identified, managed, and mitigated.',
      metrics: riskManagementMetrics
    },
    {
      id: 'operational-discipline',
      title: 'Operational Discipline',
      description: 'Measures reflecting adherence to procedures and task execution discipline.',
      metrics: operationalDisciplineMetrics
    },
    {
      id: 'process-reliability',
      title: 'Process Reliability & Efficiency',
      description: 'Performance trends on consistency, efficiency, and operational throughput.',
      metrics: processReliabilityMetrics
    },
    {
      id: 'compliance',
      title: 'Compliance & Regulatory Adherence',
      description: 'Snapshot of compliance health and regulatory adherence across functions.',
      metrics: complianceMetrics
    },
    {
      id: 'organizational-competence',
      title: 'Organizational Competence & Learning',
      description: 'Insights into workforce capability, training effectiveness, and learning culture.',
      metrics: organizationalCompetenceMetrics
    },
    {
      id: 'leadership-engagement',
      title: 'Leadership Engagement & Culture',
      description: 'Indicators reflecting leadership visibility, engagement, and cultural alignment.',
      metrics: leadershipEngagementMetrics
    },
    {
      id: 'employee-engagement',
      title: 'Employee Engagement & Wellbeing',
      description: 'Trends in workforce participation, sentiment, and wellbeing.',
      metrics: employeeEngagementMetrics
    },
    {
      id: 'supply-chain',
      title: 'Supply Chain & Contractor Performance',
      description: 'Performance overview of external vendors and contractor contributions.',
      metrics: supplyChainMetrics
    },
    {
      id: 'crisis-preparedness',
      title: 'Crisis Preparedness & Business Continuity',
      description: 'Readiness metrics related to emergency response and business resilience.',
      metrics: crisisPreparednessMetrics
    },
    {
      id: 'digital-maturity',
      title: 'Digital Maturity & Automation',
      description: 'Progress on digital transformation, technology adoption, and automation.',
      metrics: digitalMaturityMetrics
    },
    {
      id: 'continuous-improvement',
      title: 'Continuous Improvement / Innovation',
      description: 'Indicators of improvement culture, innovation uptake, and idea execution.',
      metrics: continuousImprovementMetrics
    }
  ];

  // Helper function to create tab content with only 4 metrics from selected sections
  const createLimitedTabContent = (sectionIds: string[], tabTitle: string, tabDescription: string, tabType: string) => {
    // Collect metrics from all specified sections and limit to 4 total
    const allMetrics: Array<{
      id: string;
      title: string;
      description?: string;
      value: string;
      unit: string;
      targetPercentage: number;
      trend: number[];
      isImproving: boolean;
    }> = [];

    sectionIds.forEach((sectionId) => {
      const section = performanceSections.find(s => s.id === sectionId);
      if (section) {
        allMetrics.push(...section.metrics);
      }
    });

    // Limit to first 4 metrics
    const limitedMetrics = allMetrics.slice(0, 4);

    return (
      <div className="space-y-6">
        {/* Tab Title and Description */}
        <div className="flex flex-col space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">{tabTitle}</h2>
          <p className="text-gray-600">{tabDescription}</p>
        </div>

        {/* Filter Dropdowns */}
        <div className="max-w-full">
          <div className="flex flex-wrap items-end gap-4">
            {/* Country Dropdown - Single Select */}
            <div className="space-y-2 min-w-[180px] flex-shrink-0">
              <label className="text-xs font-normal text-gray-700">
                Country <span className="text-red-500">*</span>
              </label>
              <Select value={selectedCountry} onValueChange={handleCountryChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Country" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country} value={country}>
                      {country}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Region Dropdown - Multi Select */}
            <div className="space-y-2 min-w-[200px] flex-shrink-0">
              <label className={`text-xs font-normal ${selectedCountry === '' ? 'text-gray-400' : 'text-gray-700'}`}>
                Region <span className="text-red-500">*</span>
              </label>
              <MultiSelect
                options={getRegionsForCountry(selectedCountry)}
                selected={selectedRegions}
                onChange={onRegionSelectionChange as (selected: string[]) => void}
                placeholder={selectedCountry === '' ? "Select country first" : "Select Regions"}
                disabled={selectedCountry === ''}
                className="w-full"
              />
            </div>

            {/* Site Dropdown - Multi Select */}
            <div className="space-y-2 min-w-[220px] flex-shrink-0">
              <label className={`text-xs font-normal ${selectedRegions.length === 0 ? 'text-gray-400' : 'text-gray-700'}`}>
                Site <span className="text-red-500">*</span>
              </label>
              <MultiSelect
                options={getSitesForRegions(selectedRegions)}
                selected={selectedSites}
                onChange={onSiteSelectionChange as (selected: string[]) => void}
                placeholder={selectedRegions.length === 0 ? "Select regions first" : "Select Sites"}
                disabled={selectedRegions.length === 0}
                className="w-full"
              />
            </div>

            {/* Buttons Container - Next to Site dropdown */}
            <div className="flex gap-2 items-end">
              {/* Apply Button - Only show when all selections are made */}
              {shouldShowApplyButton() && (
                <Button
                  onClick={handleApplyFilters}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 font-medium whitespace-nowrap"
                >
                  Apply Filters
                </Button>
              )}

              {/* Clear Button - Only show after filters are applied */}
              {shouldShowClearButton() && (
                <Button
                  onClick={handleClearFilters}
                  variant="outline"
                  size="sm"
                  className="px-4 py-2 font-medium border-gray-300 hover:bg-gray-50 whitespace-nowrap"
                >
                  Clear All
                </Button>
              )}
            </div>
          </div>

          {/* Applied Filters Display - Only show when filters are applied */}
          {filtersApplied && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="text-sm font-medium text-gray-700 mb-3">Applied Filters:</p>
              <div className="space-y-2">
                {/* Country Filter */}
                {appliedCountry && (
                  <div className="flex items-center gap-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Country: {appliedCountry}
                    </span>
                  </div>
                )}

                {/* Region and Site Mapping */}
                {appliedRegions.length > 0 && (
                  <div className="space-y-1">
                    {appliedRegions.map((region) => {
                      // Get the region label from the options
                      const regionOptions = getRegionsForCountry(appliedCountry);
                      const regionLabel = regionOptions.find(r => r.value === region)?.label || region;

                      // Get sites for this specific region
                      const sitesForRegion = getSitesForRegions([region]);
                      const appliedSitesForRegion = appliedSites.filter(site =>
                        sitesForRegion.some(s => s.value === site)
                      );

                      return (
                        <div key={region} className="flex items-start gap-2 flex-wrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {regionLabel}
                          </span>
                          <span className="text-xs text-gray-500 self-center">→</span>
                          <div className="flex flex-wrap gap-1">
                            {appliedSitesForRegion.map((site) => {
                              const siteLabel = sitesForRegion.find(s => s.value === site)?.label || site;
                              return (
                                <span key={site} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                  {siteLabel}
                                </span>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {limitedMetrics.map((metric) => {
            const iconAndColor = getMetricIconAndColor(metric.title, tabType);
            return (
              <MetricCard
                key={metric.id}
                {...metric}
                selectedPeriod={getSelectedPeriod(metric.id)}
                onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
                tabColor={iconAndColor.color}
                icon={iconAndColor.icon}
              />
            );
          })}
        </div>
      </div>
    );
  };

  // Tab configuration for organizing performance sections - Major Bucket approach with 4 metrics each
  const dashboardTabs = [
    {
      value: 'safety-performance',
      label: 'Safety Performance',
      content: createLimitedTabContent(
        ['safety', 'crisis-preparedness'],
        'Safety Performance',
        'Key performance indicators to track overall safety performance, incident management, and crisis preparedness across the operating entity.',
        'safety'
      )
    },
    {
      value: 'quality-performance',
      label: 'Quality Performance',
      content: createLimitedTabContent(
        ['quality', 'process-reliability'],
        'Quality Performance',
        'Indicators reflecting product and process quality, manufacturing efficiency, and operational reliability across all operations.',
        'quality'
      )
    },
    {
      value: 'environmental-performance',
      label: 'Environmental Performance',
      content: createLimitedTabContent(
        ['environmental'],
        'Environmental Performance',
        'Performance insights on environmental impact, sustainability efforts, carbon footprint, and resource management initiatives.',
        'environmental'
      )
    },
    {
      value: 'operational-discipline-efficiency',
      label: 'Operational Discipline & Efficiency',
      content: createLimitedTabContent(
        ['operational-discipline', 'digital-maturity', 'continuous-improvement'],
        'Operational Discipline & Efficiency',
        'Measures reflecting adherence to procedures, digital transformation progress, automation levels, and continuous improvement culture.',
        'operational'
      )
    },
    {
      value: 'risk-compliance-management',
      label: 'Risk & Compliance Management',
      content: createLimitedTabContent(
        ['risk-management', 'compliance', 'supply-chain'],
        'Risk & Compliance Management',
        'Overview of risk identification and mitigation effectiveness, regulatory compliance health, and supply chain performance management.',
        'risk'
      )
    },
    {
      value: 'organizational-competence-learning',
      label: 'Organizational Competence & Learning',
      content: createLimitedTabContent(
        ['organizational-competence'],
        'Organizational Competence & Learning',
        'Insights into workforce capability development, training effectiveness, knowledge management, and organizational learning culture.',
        'organizational'
      )
    },
    {
      value: 'leadership-engagement-culture',
      label: 'Leadership, Engagement & Culture',
      content: createLimitedTabContent(
        ['leadership-engagement', 'employee-engagement'],
        'Leadership, Engagement & Culture',
        'Indicators reflecting leadership visibility and engagement, workforce participation, employee sentiment, and cultural alignment across the organization.',
        'leadership'
      )
    }
  ];

  return (
    <>
      {/* Dashboard Header */}
      <div className="mb-6">
        <PageHeader
          title=" Dashboard"
        />
      </div>

      {/* Dashboard Tabs */}
      <TabsContainer
        tabs={dashboardTabs}
        defaultValue="safety-performance"
        className="mt-2"
      />
    </>
  );
};

export default HomePage;

