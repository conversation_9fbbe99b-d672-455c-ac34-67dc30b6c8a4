import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ApplicationSwitcher from '@/components/layout/ApplicationSwitcher';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import { Button } from '@/components/ui/button';
import { Plus, Search, ClipboardList, Archive, FileWarning, ChevronLeft, ChevronRight } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { API_BASE_URL } from '@/constants/index';
import apiService from '@/services/apiService';
import CreatePermitModal from '@/components/permits/CreatePermitModal';
import PermitDetailsModal from '@/components/permits/PermitDetailsModal';
import EPermitActionModal from '@/components/permits/EPermitActionModal';

// API endpoints
const PERMIT_REPORTS = API_BASE_URL + '/permit-reports';
const PERMIT_REPORT_WITH_ID = (id: string) => `${API_BASE_URL}/permit-reports/${id}`;
const ASSIGNED_ACTION_URL = (id: string) => `${API_BASE_URL}/my-assigned-actions/${id}`;

// Interface for permit data
interface PermitReport {
  id: string;
  maskId: string;
  status: string;
  permitType: string;
  permitStartDate: string;
  permitEndDate: string;
  created: string;
  locationOne?: { name: string };
  locationTwo?: { name: string };
  locationThree?: { name: string };
  locationFour?: { name: string };
  locationFive?: { name: string };
  locationSix?: { name: string };
  applicant?: { firstName: string; lastName?: string };
  assessor?: { firstName: string; lastName?: string };
  approver?: { firstName: string; lastName?: string };
  reviewer?: { firstName: string; lastName?: string };
}

// Interface for action data
interface ActionData {
  id: string;
  maskId: string;
  actionToBeTaken: string;
  actionType?: string;
  applicationId?: string;
  submittedBy: {
    firstName: string;
    lastName?: string;
  };
  applicationDetails: {
    dueDate: string;
  };
  created: string;
}

const EPermitToWorkPage = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("my-actions");
  const [permits, setPermits] = useState<PermitReport[]>([]);
  const [loading, setLoading] = useState(false);

  // Actions state
  const [actions, setActions] = useState<ActionData[]>([]);
  const [loadingActions, setLoadingActions] = useState(false);

  // Archived permits pagination state
  const [archivedPermits, setArchivedPermits] = useState<PermitReport[]>([]);
  const [loadingArchived, setLoadingArchived] = useState(false);
  const [archivedPage, setArchivedPage] = useState(1);
  const [archivedLimit] = useState(10);
  const [archivedTotal, setArchivedTotal] = useState(0);
  const [archivedTotalPages, setArchivedTotalPages] = useState(0);

  // Modal state
  const [isCreatePermitModalOpen, setIsCreatePermitModalOpen] = useState(false);
  const [isPermitDetailsModalOpen, setIsPermitDetailsModalOpen] = useState(false);
  const [selectedPermitId, setSelectedPermitId] = useState<string | null>(null);

  // Action modal state (following test.js pattern)
  const [showModal, setShowModal] = useState(false);
  const [showItem, setShowItem] = useState<ActionData | null>(null);
  const [applicationDetails, setApplicationDetails] = useState<any>(null);

  // Fetch permits from API
  const fetchPermits = useCallback(async () => {
    setLoading(true);
    try {
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "applicant" },
          { relation: "assessor" },
          { relation: "approver" },
          { relation: "reviewer" },
        ]
      };
      const url = `/permit-reports?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);
      setPermits(response);
    } catch (error) {
      console.error('Error fetching permits:', error);
      toast({
        title: "Error",
        description: "Failed to fetch permits. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Fetch archived permits with pagination
  const fetchArchivedPermits = useCallback(async (page: number, limit: number) => {
    setLoadingArchived(true);

    const uriString = {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'applicant' },
        { relation: 'assessor' },
        { relation: 'approver' },
        { relation: 'reviewer' },
      ]
    };

    const archivedUrl = `/archived-permits?page=${page}&limit=${limit}&filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    try {
      const response = await apiService.get(archivedUrl);
      const { data, total } = response;

      setArchivedPermits(data || []);
      setArchivedTotal(total || 0);
      setArchivedTotalPages(Math.ceil((total || 0) / limit));
    } catch (error) {
      console.error('Error fetching archived permits:', error);
      toast({
        title: "Error",
        description: "Failed to fetch archived permits. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingArchived(false);
    }
  }, [toast]);

  // Fetch actions from API
  const fetchActions = useCallback(async () => {
    setLoadingActions(true);
    try {
      const uriString = {
        include: [{ relation: "submittedBy" }]
      };
      const url = `${ASSIGNED_ACTION_URL('EPTW-GEN')}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);
      setActions(response || []);
    } catch (error) {
      console.error('Error fetching actions:', error);
      toast({
        title: "Error",
        description: "Failed to fetch actions. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingActions(false);
    }
  }, [toast]);

  // Handle page change for archived permits
  const handleArchivedPageChange = (newPage: number) => {
    setArchivedPage(newPage);
    fetchArchivedPermits(newPage, archivedLimit);
  };

  // Handle permit ID click
  const handlePermitIdClick = (permitId: string) => {
    setSelectedPermitId(permitId);
    setIsPermitDetailsModalOpen(true);
  };



  // openActionCard function (exact copy from test.js)
  const openActionCard = async (action: ActionData) => {
    console.log('Opening action card for:', {
      actionId: action.id,
      maskId: action.maskId,
      actionType: action.actionType,
      applicationId: action.applicationId
    });

    // First set the action to show loading state
    setShowItem(action);

    try {
      const uriString = {
        include: [
          { relation: "locationOne" },
          { relation: "locationTwo" },
          { relation: "locationThree" },
          { relation: "locationFour" },
          { relation: "locationFive" },
          { relation: "locationSix" },
          { relation: "applicant" },
          { relation: "assessor" },
          { relation: "approver" },
          { relation: "reviewer" },
        ]
      };
      const url = `${PERMIT_REPORT_WITH_ID(action.applicationId!)}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);
      if (response) {
        console.log('Fetched permit data for action:', action.maskId, 'Permit data:', {
          permitId: response.id,
          maskId: response.maskId,
          status: response.status,
          permitWorkType: response.permitWorkType
        });

        // Only set application details if we got a successful response
        setApplicationDetails(response);

        // Open the modal only after successful data fetch
        if (action.actionType === 'Reapply') {
          // For Reapply actions, open the Create Permit Modal with existing data
          setShowItem(action); // Set the action for reapply
          setIsCreatePermitModalOpen(true);
        } else {
          setShowModal(true);
        }
      } else {
        console.error('Unexpected response status');
        toast({
          title: "Error",
          description: "No permit data found for this action.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error fetching permit details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch permit details. Please try again.",
        variant: "destructive"
      });
      // Reset the action item if there's an error
      setShowItem(null);
    }
  };

  // Handle tab change to refresh data when needed
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === "archived-permits") {
      // Refresh archived permits when tab is activated
      fetchArchivedPermits(archivedPage, archivedLimit);
    } else if (value === "my-actions") {
      // Refresh actions when tab is activated
      fetchActions();
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchPermits();
    fetchActions();
    fetchArchivedPermits(1, archivedLimit); // Fetch first page of archived permits
  }, [fetchPermits, fetchActions, fetchArchivedPermits, archivedLimit]);

  // Filter permits for different tabs
  const activePermits = permits.filter(p =>
    p.status !== 'Completed' && p.status !== 'Archived' && p.status !== 'Cancelled'
  );

  // Data counts
  const actionCount = actions.length;
  const allPermitsCount = activePermits.length;
  const archivedPermitsCount = archivedTotal; // Use total from API

  // Actions table columns
  const actionsColumns = [
    {
      key: 'maskId',
      header: 'ID',
      sortable: true,
      width: '25%',
      render: (value: string, row: ActionData) => (
        <span
          className="font-medium text-blue-600 cursor-pointer hover:underline"
          onClick={() => openActionCard(row)}
        >
          {value}
        </span>
      )
    },
    {
      key: 'actionToBeTaken',
      header: 'Description',
      width: '35%'
    },
    {
      key: 'submittedBy',
      header: 'Submitted By',
      width: '20%',
      filterable: true,
      sortable: true,
      render: (value: { firstName?: string; lastName?: string }) =>
        value?.firstName ? `${value.firstName} ${value.lastName || ''}`.trim() : 'Unknown'
    },
    {
      key: 'applicationDetails',
      header: 'Due Date',
      width: '20%',
      sortable: true,
      render: (value: { dueDate?: string }) =>
        value?.dueDate ? new Date(value.dueDate).toLocaleDateString() : 'N/A'
    }
  ];

  // Helper function to format location
  const formatLocation = (permit: PermitReport) => {
    const locations = [
      permit.locationOne?.name,
      permit.locationTwo?.name,
      permit.locationThree?.name,
      permit.locationFour?.name,
      permit.locationFive?.name,
      permit.locationSix?.name
    ].filter(Boolean);
    return locations.join(' > ') || 'N/A';
  };

  // All permits table columns
  const allPermitsColumns = [
    {
      key: 'maskId',
      header: 'ID',
      sortable: true,
      width: '10%',
      render: (value: string, row: PermitReport) => (
        <span
          className="font-medium text-blue-600 cursor-pointer hover:underline"
          onClick={() => handlePermitIdClick(row.id)}
        >
          {value}
        </span>
      )
    },
    {
      key: 'status',
      header: 'Status',
      width: '10%',
      filterable: true,
      render: (value: string) => {
        const getStatusColor = (status: string) => {
          switch (status?.toLowerCase()) {
            case 'active':
            case 'approved':
              return 'bg-green-500';
            case 'pending':
              return 'bg-yellow-500';
            case 'rejected':
            case 'expired':
              return 'bg-red-500';
            default:
              return 'bg-gray-500';
          }
        };
        return (
          <Badge className={getStatusColor(value)}>
            {value}
          </Badge>
        );
      }
    },
    {
      key: 'permitType',
      header: 'Permit',
      width: '15%',
      filterable: true
    },
    {
      key: 'permitStartDate',
      header: 'Start Date',
      width: '10%',
      render: (value: string) => value ? new Date(value).toLocaleDateString() : 'N/A'
    },
    {
      key: 'permitEndDate',
      header: 'End Date',
      width: '10%',
      render: (value: string) => value ? new Date(value).toLocaleDateString() : 'N/A'
    },
    {
      key: 'created',
      header: 'Submitted Date',
      width: '10%',
      sortable: true,
      render: (value: string) => value ? new Date(value).toLocaleDateString() : 'N/A'
    },
    {
      key: 'location',
      header: 'Location',
      width: '15%',
      render: (_: unknown, row: PermitReport) => formatLocation(row)
    },
    {
      key: 'applicant',
      header: 'Applicant',
      width: '8%',
      filterable: true,
      render: (value: { firstName?: string; lastName?: string }) =>
        value?.firstName ? `${value.firstName} ${value.lastName || ''}`.trim() : 'N/A'
    },
    {
      key: 'reviewer',
      header: 'Reviewer',
      width: '8%',
      filterable: true,
      render: (value: { firstName?: string; lastName?: string }) =>
        value?.firstName ? `${value.firstName} ${value.lastName || ''}`.trim() : 'N/A'
    },
    {
      key: 'assessor',
      header: 'Assessor',
      width: '8%',
      filterable: true,
      render: (value: { firstName?: string; lastName?: string }) =>
        value?.firstName ? `${value.firstName} ${value.lastName || ''}`.trim() : 'N/A'
    },
    {
      key: 'approver',
      header: 'Approver',
      width: '8%',
      filterable: true,
      render: (value: { firstName?: string; lastName?: string }) =>
        value?.firstName ? `${value.firstName} ${value.lastName || ''}`.trim() : 'N/A'
    }
  ];



  // Define tabs for the TabsContainer
  const tabs = [
    {
      value: "my-actions",
      label: `My Actions (${actionCount})`,
      icon: <ClipboardList className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                className="pl-8"
              />
            </div>
          </div>
          {loadingActions ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">Loading actions...</span>
            </div>
          ) : (
            <ExpandableDataTable
              data={actions}
              columns={actionsColumns}
              onRowClick={(row) => openActionCard(row)}
            />
          )}
        </div>
      )
    },
    {
      value: "all-permits",
      label: `All Permits (${allPermitsCount})`,
      icon: <FileWarning className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search permits..."
                className="pl-8"
              />
            </div>
            <div className="flex gap-2">
              <Button
                className="flex items-center gap-1"
                onClick={() => setIsCreatePermitModalOpen(true)}
              >
                <Plus className="h-4 w-4" /> Create Permit
              </Button>
            </div>
          </div>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">Loading permits...</span>
            </div>
          ) : (
            <ExpandableDataTable
              data={activePermits}
              columns={allPermitsColumns}
              onRowClick={(row) => handlePermitIdClick(row.id)}
            />
          )}
        </div>
      )
    },
    {
      value: "archived-permits",
      label: `Archived Permits (${archivedPermitsCount})`,
      icon: <Archive className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search archived permits..."
                className="pl-8"
              />
            </div>
          </div>
          {loadingArchived ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">Loading archived permits...</span>
            </div>
          ) : (
            <>
              <ExpandableDataTable
                data={archivedPermits}
                columns={allPermitsColumns}
                onRowClick={(row) => handlePermitIdClick(row.id)}
              />

              {/* Pagination Controls */}
              {archivedTotalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-4 py-3 bg-white border border-gray-200 rounded-lg">
                  <div className="flex items-center text-sm text-gray-700">
                    <span>
                      Showing {((archivedPage - 1) * archivedLimit) + 1} to{' '}
                      {Math.min(archivedPage * archivedLimit, archivedTotal)} of{' '}
                      {archivedTotal} results
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleArchivedPageChange(archivedPage - 1)}
                      disabled={archivedPage <= 1}
                      className="flex items-center"
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </Button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, archivedTotalPages) }, (_, i) => {
                        let pageNum: number;
                        if (archivedTotalPages <= 5) {
                          pageNum = i + 1;
                        } else if (archivedPage <= 3) {
                          pageNum = i + 1;
                        } else if (archivedPage >= archivedTotalPages - 2) {
                          pageNum = archivedTotalPages - 4 + i;
                        } else {
                          pageNum = archivedPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === archivedPage ? "default" : "outline"}
                            size="sm"
                            onClick={() => handleArchivedPageChange(pageNum)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleArchivedPageChange(archivedPage + 1)}
                      disabled={archivedPage >= archivedTotalPages}
                      className="flex items-center"
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <PageHeader
          title="ePermit to Work"
          description="Define workflows and controls for managing high-risk work through digital permits"
        />
      </div>

      {/* Application Switcher */}
      <ApplicationSwitcher />

      <TabsContainer
        tabs={tabs}
        defaultValue="my-actions"
        onValueChange={handleTabChange}
      />

      {/* Create Permit Modal */}
      <CreatePermitModal
        isOpen={isCreatePermitModalOpen}
        onClose={() => {
          setIsCreatePermitModalOpen(false);
          // Clear reapply data when closing
          if (showItem?.actionType === 'Reapply') {
            setShowItem(null);
            setApplicationDetails(null);
          }
        }}
        onSuccess={() => {
          // Refresh permits data after successful creation
          fetchPermits();
          toast({
            title: "Success",
            description: showItem?.actionType === 'Reapply'
              ? "Permit reapplied successfully!"
              : "Permit created successfully!",
          });
          // Clear reapply data after success
          if (showItem?.actionType === 'Reapply') {
            setShowItem(null);
            setApplicationDetails(null);
          }
        }}
        reapplyAction={showItem?.actionType === 'Reapply' ? showItem : null}
        permitDetails={showItem?.actionType === 'Reapply' ? applicationDetails : null}
      />

      {/* Permit Details Modal */}
      <PermitDetailsModal
        open={isPermitDetailsModalOpen}
        onOpenChange={setIsPermitDetailsModalOpen}
        permitId={selectedPermitId}
      />

      {/* EPTW Action Modal (following test.js pattern) */}
      <EPermitActionModal
        open={showModal}
        onOpenChange={setShowModal}
        action={showItem}
        permitDetails={applicationDetails}
      />
    </div>
  );
};

export default EPermitToWorkPage;
