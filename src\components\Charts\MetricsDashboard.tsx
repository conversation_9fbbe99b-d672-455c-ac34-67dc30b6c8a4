
import { useState } from "react";
import { BarChart3, Leaf, Users, Shield } from "lucide-react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import EmissionsChart from "@/components/Charts/EmissionsChart";

const MetricsDashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});

  const overviewMetrics = [
    {
      id: "overview-direct-emissions",
      title: "YTD Direct Emissions (Scope 1 + Scope 2)",
      description: "This is a description of the indicator in greater detail. It should be set at 3 or 4 lines maximum so that the boxes are all of the same size.",
      value: "2,342.23",
      unit: "t CO₂e",
      target: 10556,
      targetPercentage: 40,
      trend: [2800, 2650, 2500, 2400, 2350, 2300, 2280, 2250, 2300, 2320, 2340, 2342],
      isImproving: true
    },
    {
      id: "overview-energy-consumption",
      title: "YTD Energy Consumption",
      description: "Total energy consumption across all facilities including electricity, natural gas, and other energy sources. Measured in megawatt hours (MWh) for standardized reporting.",
      value: "12,847.5",
      unit: "MWh",
      target: 15000,
      targetPercentage: 14.3,
      trend: [11000, 11500, 12000, 12200, 12400, 12600, 12650, 12700, 12750, 12800, 12820, 12847],
      isImproving: false
    },
    {
      id: "overview-water-usage",
      title: "YTD Water Usage",
      description: "Water consumption from all sources including municipal supply, groundwater, and recycled water. Tracking helps optimize usage and identify conservation opportunities.",
      value: "15,234",
      unit: "m³",
      target: 18000,
      targetPercentage: 15.4,
      trend: [16000, 15800, 15600, 15500, 15400, 15350, 15300, 15280, 15250, 15240, 15235, 15234],
      isImproving: true
    },
    {
      id: "overview-waste-generated",
      title: "YTD Waste Generated",
      description: "Total waste generated across all operations including hazardous and non-hazardous materials. Tracking supports waste reduction initiatives and regulatory compliance reporting.",
      value: "456.7",
      unit: "tonnes",
      target: 600,
      targetPercentage: 23.9,
      trend: [580, 560, 540, 520, 500, 490, 480, 470, 465, 460, 458, 456],
      isImproving: true
    }
  ];

  const environmentMetrics = [
    {
      id: "environment-scope1-emissions",
      title: "YTD Scope 1 Emissions",
      description: "Direct greenhouse gas emissions from sources owned or controlled by the organization. Includes combustion in boilers, furnaces, vehicles and fugitive emissions from equipment leaks.",
      value: "842.15",
      unit: "t CO₂e",
      target: 1200,
      targetPercentage: 29.8,
      trend: [1100, 1050, 1000, 950, 920, 900, 880, 860, 855, 850, 845, 842],
      isImproving: true
    },
    {
      id: "environment-scope2-emissions",
      title: "YTD Scope 2 Emissions",
      description: "Indirect greenhouse gas emissions from purchased electricity, steam, heating and cooling consumed by the organization. Reflects energy efficiency and grid carbon intensity.",
      value: "1,245.8",
      unit: "t CO₂e",
      target: 1800,
      targetPercentage: 30.8,
      trend: [1600, 1550, 1500, 1450, 1400, 1350, 1320, 1300, 1280, 1260, 1250, 1245],
      isImproving: true
    },
    {
      id: "environment-scope3-emissions",
      title: "YTD Scope 3 Emissions",
      description: "Indirect emissions from value chain activities including business travel, employee commuting, waste disposal, and purchased goods and services.",
      value: "254.28",
      unit: "t CO₂e",
      target: 350,
      targetPercentage: 27.3,
      trend: [320, 310, 300, 290, 280, 275, 270, 265, 260, 258, 256, 254],
      isImproving: true
    },
    {
      id: "environment-renewable-energy",
      title: "Renewable Energy Usage",
      description: "Percentage of total energy consumption from renewable sources including solar, wind, hydro and biomass. Key indicator for sustainability goals and carbon reduction.",
      value: "34.2",
      unit: "%",
      target: 50,
      targetPercentage: 31.6,
      trend: [20, 22, 24, 26, 28, 29, 30, 31, 32, 33, 33.5, 34.2],
      isImproving: true
    }
  ];

  const socialMetrics = [
    {
      id: "social-employee-satisfaction",
      title: "YTD Employee Satisfaction Score",
      description: "Average employee satisfaction rating from quarterly surveys covering work environment, management, career development and work-life balance. Scale of 1-10 with higher scores indicating better satisfaction.",
      value: "8.4",
      unit: "/10",
      target: 9.0,
      targetPercentage: 6.7,
      trend: [7.8, 7.9, 8.0, 8.0, 8.1, 8.1, 8.2, 8.2, 8.3, 8.3, 8.4, 8.4],
      isImproving: true
    },
    {
      id: "social-training-hours",
      title: "YTD Training Hours Completed",
      description: "Total training hours completed by all employees including safety training, technical skills development, leadership programs and compliance training. Supports continuous learning culture.",
      value: "2,847",
      unit: "hours",
      target: 3500,
      targetPercentage: 18.7,
      trend: [2200, 2300, 2400, 2500, 2600, 2650, 2700, 2750, 2780, 2800, 2820, 2847],
      isImproving: true
    },
    {
      id: "social-diversity-index",
      title: "Workplace Diversity Index",
      description: "Composite measure of workforce diversity including gender, ethnicity, age and educational background representation across all organizational levels and departments.",
      value: "72.3",
      unit: "%",
      target: 80,
      targetPercentage: 9.6,
      trend: [68, 68.5, 69, 69.5, 70, 70.5, 71, 71.5, 72, 72.1, 72.2, 72.3],
      isImproving: true
    },
    {
      id: "social-safety-incidents",
      title: "YTD Safety Incidents",
      description: "Total number of workplace safety incidents including injuries, near misses and equipment-related accidents. Lower numbers indicate improved safety performance and risk management.",
      value: "3",
      unit: "incidents",
      target: 0,
      targetPercentage: 25.0,
      trend: [6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 3],
      isImproving: true
    }
  ];

  const governanceMetrics = [
    {
      id: "governance-compliance-score",
      title: "Compliance Assessment Score",
      description: "Overall compliance rating based on regulatory requirements, internal policies and industry standards. Covers environmental, safety, financial and operational compliance areas.",
      value: "97.2",
      unit: "%",
      target: 100,
      targetPercentage: 2.8,
      trend: [94, 94.5, 95, 95.5, 96, 96.2, 96.5, 96.8, 97, 97.1, 97.1, 97.2],
      isImproving: true
    },
    {
      id: "governance-audit-completion",
      title: "YTD Audit Completion Rate",
      description: "Percentage of scheduled internal and external audits completed on time. Includes financial, operational, safety and compliance audits across all business units.",
      value: "100",
      unit: "%",
      target: 100,
      targetPercentage: 0,
      trend: [95, 96, 97, 98, 98, 99, 99, 100, 100, 100, 100, 100],
      isImproving: true
    },
    {
      id: "governance-policy-updates",
      title: "Policy Updates Implemented",
      description: "Number of organizational policies updated or newly implemented to reflect regulatory changes, best practices and operational improvements. Ensures current governance framework.",
      value: "8",
      unit: "policies",
      target: 12,
      targetPercentage: 33.3,
      trend: [4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 8, 8],
      isImproving: true
    },
    {
      id: "governance-risk-assessment",
      title: "Risk Assessment Coverage",
      description: "Percentage of identified business risks that have been formally assessed and have mitigation plans in place. Covers operational, financial, strategic and compliance risks.",
      value: "92.5",
      unit: "%",
      target: 95,
      targetPercentage: 2.6,
      trend: [88, 88.5, 89, 89.5, 90, 90.5, 91, 91.5, 92, 92.2, 92.3, 92.5],
      isImproving: true
    }
  ];

  const getMetricsForTab = (tab: string) => {
    switch (tab) {
      case "environment":
        return environmentMetrics;
      case "social":
        return socialMetrics;
      case "governance":
        return governanceMetrics;
      default:
        return overviewMetrics;
    }
  };

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  const tabs = [
    { id: "overview", label: "Overview", icon: BarChart3, color: "bg-blue-500" },
    { id: "environment", label: "Environment", icon: Leaf, color: "bg-green-500" },
    { id: "social", label: "Social", icon: Users, color: "bg-purple-500" },
    { id: "governance", label: "Governance", icon: Shield, color: "bg-orange-500" }
  ];

  return (
    <div className="space-y-6">
      {/* Clean Tab Navigation */}
      <div className="flex space-x-6 bg-gray-50 px-6 py-1 border-b border-gray-200">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.id;

          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                px-4 py-3 text-sm font-medium transition-all duration-200 relative
                ${isActive
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300'
                }
              `}
            >
              {tab.label}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {/* Headline Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {getMetricsForTab(activeTab).map((metric) => (
            <MetricCard
              key={metric.id}
              {...metric}
              showDetails={openDetailsId === metric.id}
              onToggleDetails={() => handleToggleDetails(metric.id)}
              selectedPeriod={getSelectedPeriod(metric.id)}
              onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
            />
          ))}
        </div>

        {/* Details Section - Rendered below the grid when a card is selected */}
        {openDetailsId && (() => {
          const selectedMetric = getMetricsForTab(activeTab).find(m => m.id === openDetailsId);
          return selectedMetric ? (
            <MetricDetails
              title={selectedMetric.title}
              value={selectedMetric.value}
              unit={selectedMetric.unit}
              targetPercentage={selectedMetric.targetPercentage}
              trend={selectedMetric.trend}
              isImproving={selectedMetric.isImproving}
              selectedPeriod={getSelectedPeriod(openDetailsId)}
            />
          ) : null;
        })()}

        {/* Detailed Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <EmissionsChart />
        </div>
      </div>
    </div>
  );
};

export default MetricsDashboard;
