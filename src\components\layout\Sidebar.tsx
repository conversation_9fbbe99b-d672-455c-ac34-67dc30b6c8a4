import React, { useState, useEffect, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  ClipboardList,
  LogOut,
  Settings,
  Menu,
  ChevronDown,
  ChevronUp,
  Layers,
  Users
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { fetchServices, Service } from '@/services/api';
import apiService from '@/services/apiService';

interface SidebarProps {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed, setCollapsed }) => {
  const location = useLocation();
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [applicationsOpen, setApplicationsOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [hasAdminRoles, setHasAdminRoles] = useState(false);
  const [isLoadingAdminRoles, setIsLoadingAdminRoles] = useState(true);
  const { accessToken } = useSelector((state: RootState) => state.auth);

  // Function to check if user has admin roles
  const checkAdminRoles = useCallback(async () => {
    if (!accessToken) {
      setIsLoadingAdminRoles(false);
      return;
    }

    try {
      const response = await apiService.get('/v2/users/me');
      const adminRoles = response.roles?.filter(
        (role: { maskName?: string }) => role.maskName?.toLowerCase().endsWith('_admin')
      ) || [];

      console.log('Admin roles found:', adminRoles);
      setHasAdminRoles(adminRoles.length > 0);
      setIsLoadingAdminRoles(false);
    } catch (error) {
      console.error('Error fetching admin roles:', error);
      setHasAdminRoles(false);
      setIsLoadingAdminRoles(false);
    }
  }, [accessToken]);

  useEffect(() => {
    const loadServices = async () => {
      if (!accessToken) {
        setError("Authentication token not found");
        setIsLoading(false);
        return;
      }

      try {
        const servicesData = await fetchServices(accessToken);
        setServices(servicesData);
        setIsLoading(false);
      } catch (err) {
        console.error("Error fetching services:", err);
        setError("Failed to load services");
        setIsLoading(false);
      }
    };

    loadServices();
    checkAdminRoles();
  }, [accessToken, checkAdminRoles]);

  // Close all sub-menus when sidebar is collapsed
  useEffect(() => {
    if (collapsed) {
      setApplicationsOpen(false);
      setUserMenuOpen(false);
    }
  }, [collapsed]);

  const navItems = [
    { path: '/', label: 'Home', icon: Home },
    { path: '/actions', label: 'My Actions', icon: ClipboardList },
    // { path: '/users', label: 'Users', icon: Users },
    // { path: '/role-assignment', label: 'Role Assignment', icon: UserCog },
  ];

  const bottomNavItems = [
    { path: '/settings', label: 'Settings', icon: Settings },
    { path: '/logout', label: 'Logout', icon: LogOut },
  ];

  return (
    <aside
      className={cn(
        "h-[calc(100vh-5rem)] bg-[#004466] transition-all duration-200 ease-in-out cursor-pointer flex flex-col justify-between",
        collapsed ? "w-16" : "w-64"
      )}
      onClick={() => setCollapsed(!collapsed)}
    >
      <div className="flex-1 flex flex-col overflow-y-auto pt-4">
        <nav className="space-y-1 px-3">
          {navItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <TooltipProvider key={item.path} delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link
                      to={item.path}
                      className={cn(
                        "flex items-center py-2 px-3 rounded-md mb-1 transition-colors",
                        isActive
                          ? "bg-sidebar-primary text-sidebar-primary-foreground"
                          : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                      )}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <item.icon className={cn(collapsed ? "h-8 w-8 mx-auto" : "h-5 w-5 mr-3")} />
                      {!collapsed && <span>{item.label}</span>}
                    </Link>
                  </TooltipTrigger>
                  {collapsed && <TooltipContent side="right">{item.label}</TooltipContent>}
                </Tooltip>
              </TooltipProvider>
            );
          })}

          {/* Applications Section */}
          <div className="mt-6">
            {collapsed ? (
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // If sidebar is collapsed, expand it first
                        if (collapsed) {
                          setCollapsed(false);
                        }
                        setApplicationsOpen(!applicationsOpen);
                      }}
                      className="flex items-center justify-center w-full py-2 px-3 rounded-md mb-1 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
                    >
                      <Layers className="h-6 w-6 mx-auto" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="right"
                    align="start"
                    sideOffset={12}
                    className="p-0 border bg-popover shadow-md z-50"
                  >
                    <div className="min-w-[200px] max-w-[250px]">
                      <div className="px-3 py-2 border-b bg-sidebar text-sidebar-foreground">
                        <div className="flex items-center space-x-2">
                          <Layers className="h-4 w-4" />
                          <span className="font-medium text-sm">Applications</span>
                        </div>
                      </div>
                      <div className="py-1">
                        {isLoading ? (
                          <div className="px-3 py-2 text-sm text-muted-foreground flex items-center space-x-2">
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary"></div>
                            <span>Loading...</span>
                          </div>
                        ) : error ? (
                          <div className="px-3 py-2 text-sm text-destructive flex items-center space-x-2">
                            <div className="h-3 w-3 bg-destructive rounded-full"></div>
                            <span>Error loading</span>
                          </div>
                        ) : services.length === 0 ? (
                          <div className="px-3 py-2 text-sm text-muted-foreground">
                            No applications
                          </div>
                        ) : (
                          services.map((service) => {
                            const isActive = location.pathname === service.url ||
                                            (service.url.length > 1 && location.pathname.startsWith(service.url));
                            return (
                              <Link
                                key={service.id}
                                to={service.url}
                                className={cn(
                                  "block px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground",
                                  isActive
                                    ? "bg-sidebar-primary text-sidebar-primary-foreground"
                                    : "text-popover-foreground"
                                )}
                                onClick={(e) => e.stopPropagation()}
                              >
                                <span className="truncate">{service.name}</span>
                              </Link>
                            );
                          })
                        )}
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setApplicationsOpen(!applicationsOpen);
                }}
                className="flex items-center justify-between w-full py-2 px-3 rounded-md mb-1 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
              >
                <div className="flex items-center">
                  <Layers className="h-5 w-5 mr-3" />
                  <span>Applications</span>
                </div>
                {applicationsOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            )}

            {/* Applications Submenu */}
            {!collapsed && applicationsOpen && (
              <div className="ml-8 mt-1 space-y-1">
                {isLoading ? (
                  <div className="text-sm text-sidebar-foreground/70 py-1">Loading...</div>
                ) : error ? (
                  <div className="text-sm text-red-500 py-1">Error loading applications</div>
                ) : (
                  services.map((service) => {
                    // Check if the current path starts with the service URL
                    const isActive = location.pathname === service.url ||
                                    (service.url.length > 1 && location.pathname.startsWith(service.url));
                    return (
                      <Link
                        key={service.id}
                        to={service.url}
                        className={cn(
                          "flex items-center py-1.5 px-3 rounded-md text-sm transition-colors",
                          isActive
                            ? "bg-sidebar-primary text-sidebar-primary-foreground"
                            : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                        )}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <span>{service.name}</span>
                      </Link>
                    );
                  })
                )}
              </div>
            )}

            {/* Collapsed Applications Submenu */}
            {collapsed && applicationsOpen && (
              <div className="space-y-1">
                {isLoading ? (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center justify-center py-1.5 px-3">
                          <div className="h-2 w-2 bg-sidebar-foreground/30 rounded-full animate-pulse"></div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="right">Loading applications...</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : error ? (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center justify-center py-1.5 px-3">
                          <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="right">Error loading applications</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  services.map((service) => {
                    // Check if the current path starts with the service URL
                    const isActive = location.pathname === service.url ||
                                    (service.url.length > 1 && location.pathname.startsWith(service.url));
                    return (
                      <TooltipProvider key={service.id} delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Link
                              to={service.url}
                              className={cn(
                                "flex items-center justify-center py-1.5 px-3 rounded-md transition-colors",
                                isActive
                                  ? "bg-sidebar-primary text-sidebar-primary-foreground"
                                  : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                              )}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <div className="h-2 w-2 rounded-full bg-sidebar-foreground"></div>
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent side="right">{service.name}</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })
                )}
              </div>
            )}
          </div>

          {/* Users Section - Only show if user has admin roles */}
          {!isLoadingAdminRoles && hasAdminRoles && (
          <div className="mt-6">
            {collapsed ? (
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // If sidebar is collapsed, expand it first
                        if (collapsed) {
                          setCollapsed(false);
                        }
                        setUserMenuOpen(!userMenuOpen);
                      }}
                      className="flex items-center justify-center w-full py-2 px-3 rounded-md mb-1 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
                    >
                      <Users className="h-6 w-6 mx-auto" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="right"
                    align="start"
                    sideOffset={12}
                    className="p-0 border bg-popover shadow-md z-50"
                  >
                    <div className="min-w-[200px] max-w-[250px]">
                      <div className="px-3 py-2 border-b bg-sidebar text-sidebar-foreground">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4" />
                          <span className="font-medium text-sm">Users</span>
                        </div>
                      </div>
                      <div className="py-1">
                        <Link
                          to="/role-assignment"
                          className={cn(
                            "block px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground",
                            location.pathname === "/role-assignment"
                              ? "bg-sidebar-primary text-sidebar-primary-foreground"
                              : "text-popover-foreground"
                          )}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <span className="truncate">Role Assignment</span>
                        </Link>
                        <Link
                          to="/users?tab=external-users"
                          className={cn(
                            "block px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground",
                            location.pathname === "/users" && location.search.includes("external-users")
                              ? "bg-sidebar-primary text-sidebar-primary-foreground"
                              : "text-popover-foreground"
                          )}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <span className="truncate">External Users</span>
                        </Link>
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setUserMenuOpen(!userMenuOpen);
                }}
                className="flex items-center justify-between w-full py-2 px-3 rounded-md mb-1 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
              >
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-3" />
                  <span>Users</span>
                </div>
                {userMenuOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            )}

            {/* Users Submenu */}
            {!collapsed && userMenuOpen && (
              <div className="ml-8 mt-1 space-y-1">
                <Link
                  to="/role-assignment"
                  className={cn(
                    "flex items-center py-1.5 px-3 rounded-md text-sm transition-colors",
                    location.pathname === "/role-assignment"
                      ? "bg-sidebar-primary text-sidebar-primary-foreground"
                      : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                  )}
                  onClick={(e) => e.stopPropagation()}
                >
                  <span>Role Assignment</span>
                </Link>
                <Link
                  to="/users?tab=external-users"
                  className={cn(
                    "flex items-center py-1.5 px-3 rounded-md text-sm transition-colors",
                    location.pathname === "/users" && location.search.includes("external-users")
                      ? "bg-sidebar-primary text-sidebar-primary-foreground"
                      : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                  )}
                  onClick={(e) => e.stopPropagation()}
                >
                  <span>External Users</span>
                </Link>
              </div>
            )}

            {/* Collapsed Users Submenu */}
            {collapsed && userMenuOpen && (
              <div className="space-y-1">
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/role-assignment"
                        className={cn(
                          "flex items-center justify-center py-1.5 px-3 rounded-md transition-colors",
                          location.pathname === "/role-assignment"
                            ? "bg-sidebar-primary text-sidebar-primary-foreground"
                            : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                        )}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="h-2 w-2 rounded-full bg-sidebar-foreground"></div>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right">Role Assignment</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/users?tab=external-users"
                        className={cn(
                          "flex items-center justify-center py-1.5 px-3 rounded-md transition-colors",
                          location.pathname === "/users" && location.search.includes("external-users")
                            ? "bg-sidebar-primary text-sidebar-primary-foreground"
                            : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                        )}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="h-2 w-2 rounded-full bg-sidebar-foreground"></div>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right">External Users</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            )}
          </div>
          )}
        </nav>
      </div>

      <div className="border-t border-sidebar-border py-4">
        <nav className="space-y-1 px-3">
          {bottomNavItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <TooltipProvider key={item.path} delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link
                      to={item.path}
                      className={cn(
                        "flex items-center py-2 px-3 rounded-md transition-colors",
                        isActive
                          ? "bg-sidebar-primary text-sidebar-primary-foreground"
                          : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                      )}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <item.icon className={cn(collapsed ? "h-8 w-8 mx-auto" : "h-5 w-5 mr-3")} />
                      {!collapsed && <span>{item.label}</span>}
                    </Link>
                  </TooltipTrigger>
                  {collapsed && <TooltipContent side="right">{item.label}</TooltipContent>}
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </nav>
      </div>

      {/* Mobile menu trigger - only visible on small screens */}
      <div className="lg:hidden absolute top-4 right-4 z-50">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-white shadow-md"
          onClick={() => setCollapsed(!collapsed)}
        >
          <Menu className="h-5 w-5" />
        </Button>
      </div>
    </aside>
  );
};

export default Sidebar;
