import { useState, useEffect } from 'react';
import { getFileDisplayUrl } from '@/utils/fileUtils';

interface UsePresignedUrlOptions {
  fileName?: string;
  originalFileName?: string;
  src?: string;
  enabled?: boolean;
}

interface PresignedUrlState {
  url: string | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook to fetch presigned URLs for file display
 * Handles both direct URLs and server file names
 */
export const usePresignedUrl = (options: UsePresignedUrlOptions) => {
  const { fileName, originalFileName, src, enabled = true } = options;
  
  const [state, setState] = useState<PresignedUrlState>({
    url: null,
    isLoading: false,
    error: null
  });

  useEffect(() => {
    if (!enabled) {
      setState({ url: null, isLoading: false, error: null });
      return;
    }

    const fetchPresignedUrl = async () => {
      // Priority 1: If we have originalFileName (server filename), always fetch fresh presigned URL
      if (originalFileName) {
        setState(prev => ({ ...prev, isLoading: true, error: null }));

        try {
          const presignedUrl = await getFileDisplayUrl(originalFileName);
          setState({ url: presignedUrl, isLoading: false, error: null });
          return;
        } catch (error) {
          console.error('Failed to fetch presigned URL for originalFileName:', originalFileName, error);
          // Continue to fallback options
        }
      }

      // Priority 2: If we have fileName (might be server filename), try to fetch presigned URL
      if (fileName) {
        setState(prev => ({ ...prev, isLoading: true, error: null }));

        try {
          const presignedUrl = await getFileDisplayUrl(fileName);
          setState({ url: presignedUrl, isLoading: false, error: null });
          return;
        } catch (error) {
          console.error('Failed to fetch presigned URL for fileName:', fileName, error);
          // Continue to fallback options
        }
      }

      // Priority 3: If src is already a full URL (http/https), use it directly
      if (src && (src.startsWith('http://') || src.startsWith('https://'))) {
        setState({ url: src, isLoading: false, error: null });
        return;
      }

      // Priority 4: Use src as fallback
      if (src) {
        setState({ url: src, isLoading: false, error: null });
      } else {
        setState({
          url: null,
          isLoading: false,
          error: 'No file information available'
        });
      }
    };

    fetchPresignedUrl();
  }, [fileName, originalFileName, src, enabled]);

  return state;
};

/**
 * Hook specifically for image presigned URLs
 */
export const useImagePresignedUrl = (imageData: any) => {
  return usePresignedUrl({
    fileName: imageData?.fileName,
    originalFileName: imageData?.originalFileName,
    src: imageData?.src,
    enabled: !!(imageData?.fileName || imageData?.originalFileName || imageData?.src)
  });
};

/**
 * Hook specifically for video presigned URLs
 */
export const useVideoPresignedUrl = (videoData: any) => {
  return usePresignedUrl({
    fileName: videoData?.fileName,
    originalFileName: videoData?.originalFileName,
    src: videoData?.src,
    enabled: !!(videoData?.fileName || videoData?.originalFileName || videoData?.src)
  });
};

/**
 * Hook specifically for audio presigned URLs
 */
export const useAudioPresignedUrl = (audioData: any) => {
  return usePresignedUrl({
    fileName: audioData?.fileName,
    originalFileName: audioData?.originalFileName,
    src: audioData?.src,
    enabled: !!(audioData?.fileName || audioData?.originalFileName || audioData?.src)
  });
};

/**
 * Hook specifically for attachment presigned URLs
 */
export const useAttachmentPresignedUrl = (attachmentData: any) => {
  return usePresignedUrl({
    fileName: attachmentData?.fileName,
    originalFileName: attachmentData?.originalFileName,
    src: attachmentData?.src,
    enabled: !!(attachmentData?.fileName || attachmentData?.originalFileName || attachmentData?.src)
  });
};
