import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import ApplicationSwitcher from '@/components/layout/ApplicationSwitcher';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { FileText, Plus, Search, AlertCircle, Calendar, User, MapPin } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const IncidentInvestigationPage = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Incident Investigation"
          description="Systematically analyze incidents to identify root causes and prevent recurrence"
        />
        <Button className="flex items-center gap-1" onClick={() => 
          toast({ 
            title: "Report Incident", 
            description: "Incident reporting functionality will be implemented soon." 
          })
        }>
          <Plus className="h-4 w-4" /> Report Incident
        </Button>
      </div>

      {/* Application Switcher */}
      <ApplicationSwitcher />

      <Tabs defaultValue="my-investigations" className="w-full">
        <TabsList className="grid w-full md:w-[600px] grid-cols-3">
          <TabsTrigger value="my-investigations">My Investigations</TabsTrigger>
          <TabsTrigger value="all-incidents">All Incidents</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-investigations" className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search my investigations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">INC-2023-0045</CardTitle>
                    <CardDescription>Near Miss - Equipment Malfunction</CardDescription>
                  </div>
                  <Badge className="bg-amber-500 text-white">In Progress</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                    <p className="text-sm text-muted-foreground">
                      Conveyor belt stopped unexpectedly during operation, causing production delay.
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span>May 15, 2023</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span>Production Line A</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3 text-muted-foreground" />
                      <span>Reported by: J. Smith</span>
                    </div>
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                      <FileText className="h-3 w-3" /> Continue Investigation
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">INC-2023-0039</CardTitle>
                    <CardDescription>First Aid Injury - Slip and Fall</CardDescription>
                  </div>
                  <Badge className="bg-blue-500">Under Review</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
                    <p className="text-sm text-muted-foreground">
                      Employee slipped on wet floor in break room, resulting in minor wrist sprain.
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span>May 12, 2023</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span>Admin Building</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3 text-muted-foreground" />
                      <span>Reported by: M. Johnson</span>
                    </div>
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                      <FileText className="h-3 w-3" /> View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="all-incidents" className="space-y-4">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">All Incidents</h2>
            <p className="text-muted-foreground">
              This tab will display a comprehensive list of all reported incidents.
            </p>
          </div>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Incident Analytics</h2>
            <p className="text-muted-foreground">
              This tab will provide statistical analysis and trends of incidents over time.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IncidentInvestigationPage;
