
import React, { useState, useRef } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

interface Tab {
  value: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
}

interface TabsContainerProps {
  tabs: Tab[];
  defaultValue?: string;
  className?: string;
  onValueChange?: (value: string) => void;
}

const TabsContainer: React.FC<TabsContainerProps> = ({
  tabs,
  defaultValue = tabs[0]?.value,
  className = "",
  onValueChange
}) => {
  const [activeTab, setActiveTab] = useState(defaultValue);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handleValueChange = (value: string) => {
    setActiveTab(value);
    if (onValueChange) {
      onValueChange(value);
    }
  };

  return (
    <Tabs
      defaultValue={defaultValue}
      value={activeTab}
      onValueChange={handleValueChange}
      className={`w-full overflow-y-visible ${className}`}
    >
      <div className="border-b mb-6 overflow-y-visible">
        {/* Scrollable Tabs List */}
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto overflow-y-hidden scrollbar-hide"
        >
          <TabsList className="bg-transparent w-max min-w-full justify-start">
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="relative data-[state=active]:shadow-none rounded-none px-4 py-3 data-[state=active]:bg-transparent data-[state=active]:text-blue-600 data-[state=active]:font-semibold whitespace-nowrap flex-shrink-0 border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700 text-gray-500 transition-all duration-200 data-[state=active]:after:absolute data-[state=active]:after:bottom-0 data-[state=active]:after:left-0 data-[state=active]:after:right-0 data-[state=active]:after:h-1 data-[state=active]:after:bg-blue-600 data-[state=active]:after:content-[''] data-[state=active]:after:rounded-t-sm"
              >
                <div className="flex items-center gap-2">
                  {tab.icon}
                  {tab.label}
                </div>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </div>

      {tabs.map((tab) => (
        <TabsContent key={tab.value} value={tab.value} className="pt-2 animate-fade-in overflow-y-visible">
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default TabsContainer;
